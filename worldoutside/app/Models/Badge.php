<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Model odznaku v aplikácii WorldOutside
 *
 * Reprezentuje odznak, ktorý môžu používatelia získať za rôzne úspechy
 * pri navštevovaní vrcholov (napr. počet navštívených miest, výška vrcholov).
 * Odznaky slúžia ako motivačný systém pre používateľov.
 *
 * @property int $id Jedinečný identifikátor odznaku
 * @property string $name Názov odznaku
 * @property string $slug Jedinečný identifikátor odznaku (URL-friendly)
 * @property string|null $description Popis odznaku a podmienok získania
 * @property string|null $icon URL ikony alebo názov ikony odznaku
 * @property \Illuminate\Support\Carbon|null $created_at Čas vytvorenia záznamu
 * @property \Illuminate\Support\Carbon|null $updated_at Čas poslednej aktualizácie
 */
class Badge extends Model
{
    use HasFactory;

    /**
     * Atribúty, ktoré môžu byť hromadne priradené (mass assignment)
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',        // Názov odznaku (napr. "Prvých 5 vrcholov")
        'slug',        // Jedinečný identifikátor (napr. "first-5-peaks")
        'description', // Popis odznaku a podmienok získania
        'icon',        // URL ikony alebo názov ikony
    ];

    /**
     * Vzťah many-to-many s používateľmi
     *
     * Definuje vzťah medzi odznakom a používateľmi, ktorí ho získali.
     * Pivot tabuľka obsahuje informáciu o čase udelenia odznaku.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_badge')
            ->withTimestamps()           // Automatické created_at a updated_at
            ->withPivot('awarded_at');   // Čas udelenia odznaku používateľovi
    }
}
