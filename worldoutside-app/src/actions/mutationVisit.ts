import { panic, success } from "../misc/notif";

/**
 * Mutation funkcia pre označenie miesta ako navštíveného
 *
 * Táto funkcia odošle POST požiadavku na backend API pre označenie
 * konkrétneho miesta ako navštíveného aktuálnym používateľom.
 * Automaticky spracováva chyby a zobrazuje notifikácie používateľovi.
 *
 * @param id ID miesta, ktoré sa má označiť ako navštívené
 * @returns Promise s odpoveďou z API alebo undefined pri chybe
 */
export function mutationVisit(id: number) {
  // HTTP POST požiadavka na backend API endpoint pre označenie návštevy
  return fetch(`${process.env.REACT_APP_API_URL}/places/${id}/visit`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      // Autentifikácia pomocou Bearer tokenu z localStorage
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      "Content-Type": "application/json",
    },
  })
    .then(async (response) => {
      // Špecifická kontrola pre chybu autentifikácie (401/400)
      if (response.status === 400) {
        throw new Error("Pre označenie miesta sa musíte prihlásiť");
      }

      // Kontrola ostatných chýb
      if (!response.ok) {
        // Pokus o parsovanie chybovej správy z backendu
        let msg = "Nepodarilo sa získať dáta";
        try {
          const data = await response.json();
          // Špecifické spracovanie známych chybových správ
          if (data && data.message === "Place already visited") {
            msg = "Toto miesto ste už označili ako navštívené.";
          } else if (data && data.message) {
            msg = data.message;
          }
        } catch {
          // Ak sa nepodarí parsovať JSON, použije sa predvolená správa
        }
        throw new Error(msg);
      }

      // Zobrazenie úspešnej notifikácie používateľovi
      success({
        title: "Miesto označené",
        message: "Miesto bolo úspešne označené",
      });

      // Vrátenie JSON odpovede z API
      return response.json();
    })
    .catch((error) => {
      // Zobrazenie chybovej notifikácie používateľovi
      panic({
        title: "Nepodarilo sa označiť miesto",
        message: error.message,
      });
      // Funkcia nevracia nič pri chybe (undefined)
    });
}
