
import { Marker, Popup } from "react-leaflet";
import MarkerClusterGroup from "react-leaflet-cluster";
import { finishedMarkerIcon, notFinishedMarkerIcon, softVisitedMarkerIcon } from "./MarkerIcons";
import { useMutation } from "@tanstack/react-query";
import { addPlannedPlace, removePlannedPlace } from "../../actions/getPlacesPlanned";
import { useState } from "react";
import PhotoVerificationModal from "./PhotoVerificationModal";
import EnhancedPopup from "./EnhancedPopup";

export default function Markers({
  data,
  handleVisitedClick,
  handleVerifyClick,
  verifying,
  softVisitedIds,
  plannedIds = [],
  refetchPlanned,
  refetchAll,
}: {
  data: any;
  handleVisitedClick: (id: number) => void;
  handleVerifyClick: (id: number) => void;
  verifying: number | null;
  softVisitedIds: number[];
  plannedIds?: number[];
  refetchPlanned?: () => void;
  refetchAll?: () => void;
}) {
  const addMutation = useMutation({
    mutationFn: addPlannedPlace,
    onSuccess: () => refetchPlanned && refetchPlanned(),
  });
  const removeMutation = useMutation({
    mutationFn: removePlannedPlace,
    onSuccess: () => {
      refetchPlanned && refetchPlanned();
      refetchAll && refetchAll();
    },
  });
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [selectedPlaceId, setSelectedPlaceId] = useState<number | null>(null);

  const handlePhotoVerifyClick = (id: number) => {
    setSelectedPlaceId(id);
    setPhotoModalOpen(true);
  };

  const handlePhotoModalClose = () => {
    setPhotoModalOpen(false);
    setSelectedPlaceId(null);
  };

  const handlePhotoSuccess = () => {
    // Optionally refetch or show notification
  };

  return <>
    {/* Marker clustering for better performance and readability */}
    <MarkerClusterGroup
      chunkedLoading
      iconCreateFunction={(cluster: any) => {
        const count = cluster.getChildCount();
        let size = 'small';
        if (count >= 10) size = 'medium';
        if (count >= 25) size = 'large';

        return new (window as any).L.DivIcon({
          html: `<div class="cluster-marker cluster-${size}"><span>${count}</span></div>`,
          className: 'custom-cluster-icon',
          iconSize: new (window as any).L.Point(40, 40, true)
        });
      }}
      spiderfyOnMaxZoom={true}
      showCoverageOnHover={false}
      zoomToBoundsOnClick={true}
      maxClusterRadius={50}
    >
      {data.map((place: any, idx: number) => {
        if (idx === 0) {
          // TEMP: Log the first place object to inspect available fields
          // Remove this after inspection
          // eslint-disable-next-line no-console
          console.log('Sample place object:', place);
        }
        // Determine visit type and planned status
        const visitType = place.visit_type ||
                         (softVisitedIds.includes(place.id) ? 'soft' :
                         (place.visited ? 'verified' : null));
        const isPlanned = plannedIds.includes(place.id);

        // Select appropriate marker icon
        let icon = notFinishedMarkerIcon;
        if (visitType === 'verified') {
          icon = finishedMarkerIcon;
        } else if (visitType === 'soft') {
          icon = softVisitedMarkerIcon;
        }

        return (
          <Marker
            key={place.id}
            position={[parseFloat(place.latitude), parseFloat(place.longitude)]}
            icon={icon}
          >
            {/* Visitor count badge overlay */}
            {typeof place.visitor_count === 'number' && (
              <div
                className="wo-marker-badge"
                aria-label={`${place.visitor_count} people have visited this location`}
                style={{
                  position: 'absolute',
                  right: 0,
                  bottom: 0,
                  transform: 'translate(40%, 40%)',
                  background: 'var(--color-primary-600)',
                  color: 'var(--color-neutral-50)',
                  borderRadius: 'var(--radius-lg)',
                  boxShadow: 'var(--shadow-md)',
                  padding: '2px 7px',
                  fontSize: 13,
                  fontWeight: 600,
                  minWidth: 22,
                  minHeight: 22,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 1000,
                  border: '2px solid var(--color-neutral-50)',
                  pointerEvents: 'none',
                  userSelect: 'none',
                }}
              >
                {place.visitor_count}
              </div>
            )}
            <Popup minWidth={340} maxWidth={400}>
              <EnhancedPopup
                place={place}
                visitType={visitType}
                isPlanned={isPlanned}
                verifying={verifying}
                onVisitedClick={handleVisitedClick}
                onVerifyClick={handleVerifyClick}
                onPhotoVerifyClick={handlePhotoVerifyClick}
                onAddPlanned={(id) => addMutation.mutate(id)}
                onRemovePlanned={(id) => removeMutation.mutate(id)}
                addMutationPending={addMutation.isPending}
                removeMutationPending={removeMutation.isPending}
              />
            </Popup>
          </Marker>
        );
      })}
    </MarkerClusterGroup>
  <PhotoVerificationModal
    opened={photoModalOpen}
    onClose={handlePhotoModalClose}
    placeId={selectedPlaceId}
    onSuccess={handlePhotoSuccess}
  />
  </>;
}
