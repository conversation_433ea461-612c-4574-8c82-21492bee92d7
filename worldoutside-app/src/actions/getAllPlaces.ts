import { queryOptions } from "@tanstack/react-query";

export function getAllPlacesOptions() {
  return queryOptions({
    queryKey: ["places/all"],
    queryFn: getAllPlaces,
    staleTime: 60 * 60 * 1000, // 1 hour cache
  });
}

async function getAllPlaces() {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places`, {
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
  });
  if (!response.ok) throw new Error("Nepodarilo sa získať všetky vrcholy");
  const data = (await response.json()) as { data: Place[] };
  return data.data;
}
