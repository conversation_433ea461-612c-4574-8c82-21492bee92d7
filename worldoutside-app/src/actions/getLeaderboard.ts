// Fetch leaderboard data from the backend API
import { useQuery } from "@tanstack/react-query";

export interface LeaderboardUser {
  id: number;
  name: string;
  avatar: string | null;
  peaks: number;
  elevation: number;
  badges: { slug: string; name: string; icon?: string }[];
  isCurrentUser: boolean;
}

export interface LeaderboardResponse {
  data: LeaderboardUser[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  regions?: string[];
}

export async function getLeaderboard({
  metric = "peaks",
  region = "all",
  period = "all",
  page = 1,
  per_page = 10,
}: {
  metric?: string;
  region?: string;
  period?: string;
  page?: number;
  per_page?: number;
}): Promise<LeaderboardResponse> {
  const token = localStorage.getItem("wo-token");
  const params = new URLSearchParams({
    metric,
    region: region === "all" ? "" : region,
    period,
    page: String(page),
    per_page: String(per_page),
  });
  const res = await fetch(`${process.env.REACT_APP_API_URL}/leaderboard?${params.toString()}`, {
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
    },
  });
  if (!res.ok) throw new Error("Failed to load leaderboard");
  return res.json();
}

export function useLeaderboardQuery(filters: {
  metric?: string;
  region?: string;
  period?: string;
  page?: number;
  per_page?: number;
}) {
  return useQuery({
    queryKey: ["leaderboard", filters],
    queryFn: () => getLeaderboard(filters),
    // keepPreviousData: true, // Uncomment if your react-query version supports it
  });
}
