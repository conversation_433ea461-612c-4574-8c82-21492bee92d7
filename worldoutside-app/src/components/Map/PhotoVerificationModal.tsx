import { Modal, Button, FileInput, Stack, Text, Group, Alert, Progress } from "@mantine/core";
import { useState } from "react";
import { IconPhoto, IconMapPin, IconAlertCircle, IconCheck } from "@tabler/icons-react";
// @ts-ignore
import EXIF from "exif-js";

/**
 * PhotoVerificationModal Component
 *
 * Modern modal for photo verification with:
 * - Drag & drop file upload
 * - EXIF data extraction and display
 * - GPS coordinate validation
 * - Upload progress indication
 * - Error handling with user-friendly messages
 * - Modern UI with smooth animations
 */

interface PhotoVerificationModalProps {
  opened: boolean;
  onClose: () => void;
  placeId: number | null;
  onSuccess: () => void;
}

export default function PhotoVerificationModal({ opened, onClose, placeId, onSuccess }: PhotoVerificationModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [exifData, setExifData] = useState<any>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (f: File | null) => {
    setFile(f);
    setError(null);
    setExifData(null);
    if (f) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const img = new window.Image();
        img.onload = function () {
          EXIF.getData(img as any, function (this: any) {
            const allExif = EXIF.getAllTags(this);
            console.log(allExif);
            setExifData(allExif);
          });
        };
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(f);
    }
  };

  const handleSubmit = async () => {
    if (!file || !placeId) return;
    setUploading(true);
    setError(null);
    try {
      const formData = new FormData();
      formData.append("photo", file);
      formData.append("place_id", String(placeId));
      formData.append("exif", JSON.stringify(exifData));
      const res = await fetch(`${process.env.REACT_APP_API_URL}/places/photo-verification`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
        },
        body: formData,
      });
      if (!res.ok) throw new Error("Nepodarilo sa odoslať fotku");
      onSuccess();
      onClose();
    } catch (e: any) {
      setError(e.message || "Chyba pri nahrávaní");
    } finally {
      setUploading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="sm" align="center">
          <div
            style={{
              padding: 'var(--spacing-xs)',
              borderRadius: 'var(--radius-md)',
              background: 'var(--color-primary-100)',
              color: 'var(--color-primary-600)',
            }}
          >
            <IconPhoto size={20} />
          </div>
          <div>
            <Text fw={600} size="lg" style={{ color: 'var(--color-neutral-800)' }}>
              Overenie fotkou
            </Text>
            <Text size="sm" style={{ color: 'var(--color-neutral-500)' }}>
              Nahrajte fotku s GPS údajmi
            </Text>
          </div>
        </Group>
      }
      centered
      zIndex={3000}
      radius="xl"
      size="md"
      styles={{
        content: {
          borderRadius: 'var(--radius-xl)',
          border: '1px solid var(--color-neutral-200)',
        },
        header: {
          borderBottom: '1px solid var(--color-neutral-200)',
          paddingBottom: 'var(--spacing-md)',
          marginBottom: 'var(--spacing-lg)',
        },
      }}
    >
      <Stack gap="lg">
        {/* Modern file input */}
        <FileInput
          label="Vyberte fotku z vašej galérie"
          description="Fotka musí obsahovať GPS údaje (polohu) pre úspešné overenie"
          placeholder="Kliknite pre výber súboru alebo ho pretiahnite sem"
          accept="image/*"
          value={file}
          onChange={handleFileChange}
          required
          radius="lg"
          size="md"
          leftSection={<IconPhoto size={18} />}
          styles={{
            input: {
              border: '2px dashed var(--color-neutral-300)',
              backgroundColor: 'var(--color-neutral-50)',
              transition: 'all var(--transition-fast)',
              minHeight: '60px',
              '&:hover': {
                borderColor: 'var(--color-primary-400)',
                backgroundColor: 'var(--color-primary-50)',
              },
              '&:focus': {
                borderColor: 'var(--color-primary-500)',
                backgroundColor: 'white',
                boxShadow: '0 0 0 3px var(--color-primary-100)',
              },
            },
            label: {
              fontSize: '14px',
              fontWeight: 600,
              color: 'var(--color-neutral-700)',
              marginBottom: 'var(--spacing-xs)',
            },
            description: {
              fontSize: '12px',
              color: 'var(--color-neutral-500)',
              marginBottom: 'var(--spacing-sm)',
            },
          }}
        />

        {/* EXIF data display */}
        {exifData && (
          <Alert
            icon={exifData.GPSLatitude && exifData.GPSLongitude ? <IconCheck size={16} /> : <IconAlertCircle size={16} />}
            color={exifData.GPSLatitude && exifData.GPSLongitude ? "green" : "yellow"}
            radius="lg"
            styles={{
              root: {
                border: `1px solid ${exifData.GPSLatitude && exifData.GPSLongitude ? 'var(--color-success-200)' : 'var(--color-warning-200)'}`,
                backgroundColor: exifData.GPSLatitude && exifData.GPSLongitude ? 'var(--color-success-50)' : 'var(--color-warning-50)',
              },
            }}
          >
            <Group gap="xs" align="center">
              <IconMapPin size={14} />
              <Text size="sm" fw={500}>
                {exifData.GPSLatitude && exifData.GPSLongitude
                  ? `GPS súradnice nájdené: ${exifData.GPSLatitude}, ${exifData.GPSLongitude}`
                  : "GPS údaje v fotke nenájdené - overenie nebude možné"
                }
              </Text>
            </Group>
          </Alert>
        )}

        {/* Upload progress */}
        {uploading && (
          <div>
            <Text size="sm" mb="xs" style={{ color: 'var(--color-neutral-600)' }}>
              Nahrávanie fotky...
            </Text>
            <Progress
              value={100}
              animated
              radius="lg"
              size="md"
              color="blue"
              styles={{
                root: {
                  backgroundColor: 'var(--color-neutral-200)',
                },
              }}
            />
          </div>
        )}

        {/* Error display */}
        {error && (
          <Alert
            icon={<IconAlertCircle size={16} />}
            color="red"
            radius="lg"
            styles={{
              root: {
                border: '1px solid var(--color-error-200)',
                backgroundColor: 'var(--color-error-50)',
              },
            }}
          >
            <Text size="sm" fw={500}>
              {error}
            </Text>
          </Alert>
        )}

        {/* Action buttons */}
        <Group justify="flex-end" gap="sm">
          <Button
            variant="subtle"
            color="gray"
            onClick={onClose}
            radius="lg"
            disabled={uploading}
          >
            Zrušiť
          </Button>
          <Button
            onClick={handleSubmit}
            loading={uploading}
            disabled={!file || (exifData && !exifData.GPSLatitude)}
            radius="lg"
            leftSection={<IconCheck size={16} />}
            styles={{
              root: {
                background: 'var(--color-primary-500)',
                '&:hover': {
                  background: 'var(--color-primary-600)',
                  transform: 'translateY(-1px)',
                },
                '&:disabled': {
                  background: 'var(--color-neutral-300)',
                  color: 'var(--color-neutral-500)',
                },
              },
            }}
          >
            Odoslať na overenie
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
