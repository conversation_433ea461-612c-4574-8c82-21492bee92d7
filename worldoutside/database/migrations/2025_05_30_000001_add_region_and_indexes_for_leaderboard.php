<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Add region column to places if not exists
        if (!Schema::hasColumn('places', 'region')) {
            Schema::table('places', function (Blueprint $table) {
                $table->string('region')->nullable()->after('elevation')->index();
            });
        }
        // Add indexes for leaderboard performance
        Schema::table('place_user', function (Blueprint $table) {
            $table->index(['user_id', 'visit_type', 'created_at']);
            $table->index(['place_id', 'visit_type', 'created_at']);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('places', 'region')) {
            Schema::table('places', function (Blueprint $table) {
                $table->dropColumn('region');
            });
        }
        Schema::table('place_user', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'visit_type', 'created_at']);
            $table->dropIndex(['place_id', 'visit_type', 'created_at']);
        });
    }
};
