<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Place;
use App\Models\Badge;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LeaderboardDemoSeeder extends Seeder
{
    public function run(): void
    {
        // Create demo regions
        $regions = ['Tatras', 'Fatra', 'Slovakia', 'Alps'];

        // Create demo places
        foreach ($regions as $region) {
            for ($i = 1; $i <= 10; $i++) {
                Place::factory()->create([
                    'name' => $region . ' Peak ' . $i,
                    'region' => $region,
                    'elevation' => rand(1000, 2500),
                ]);
            }
        }

        // Create demo users
        User::factory(10)->create()->each(function ($user) use ($regions) {
            // Each user visits places from a random subset of regions
            $userRegions = collect($regions)->shuffle()->take(rand(2, count($regions)))->values();
            foreach ($userRegions as $region) {
                // For each region, pick a random number of places (2-7)
                $places = Place::where('region', $region)->inRandomOrder()->limit(rand(2, 7))->get();
                foreach ($places as $place) {
                    $user->places()->attach($place->id, [
                        'visit_type' => 'verified',
                        'created_at' => now()->subDays(rand(0, 365)),
                    ]);
                }
            }
        });

        // Create demo badges
        $badges = [
            ['name' => 'Summit Master', 'slug' => 'summit', 'icon' => 'summit.svg'],
            ['name' => 'Marathoner', 'slug' => 'marathon', 'icon' => 'marathon.svg'],
            ['name' => 'Winter Climber', 'slug' => 'winter', 'icon' => 'winter.svg'],
            ['name' => 'Speedster', 'slug' => 'speedster', 'icon' => 'speedster.svg'],
        ];
        foreach ($badges as $badge) {
            Badge::firstOrCreate(['slug' => $badge['slug']], $badge);
        }

        // Assign random badges to users
        $allBadges = Badge::all();
        User::all()->each(function ($user) use ($allBadges) {
            $badgeIds = $allBadges->random(rand(1, $allBadges->count()))->pluck('id')->toArray();
            $syncData = [];
            foreach ($badgeIds as $badgeId) {
                $syncData[$badgeId] = ['awarded_at' => now()];
            }
            $user->badges()->syncWithoutDetaching($syncData);
        });
    }
}
