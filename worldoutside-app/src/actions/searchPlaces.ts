import { queryOptions } from "@tanstack/react-query";

export function getPlacesSearchOptions(query: string) {
  return queryOptions({
    queryKey: ["places/search", query],
    queryFn: () => searchPlaces(query),
    staleTime: 0,
  });
}

async function searchPlaces(query: string) {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places/search?query=${encodeURIComponent(query)}`, {
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
  });
  if (!response.ok) throw new Error("Nepodarilo sa vyhľadať vrcholy");
  const data = (await response.json()) as { data: Place[] };
  return data.data;
}
