import React from "react";
import { UnstyledButton } from "@mantine/core";

/**
 * NavItem Component
 *
 * Modern navigation item with:
 * - Clean, minimalist design with subtle hover effects
 * - Active state visual indicators
 * - Smooth transitions and micro-interactions
 * - Accessible design with proper focus states
 * - Responsive layout that adapts to different screen sizes
 *
 * @param href - The URL to navigate to
 * @param children - The content to display (icon + text)
 * @param isActive - Whether this nav item is currently active
 * @param ...props - Additional props passed to the button
 */

interface NavItemProps {
  href: string;
  children: React.ReactNode;
  isActive?: boolean;
  [key: string]: any; // For additional props like aria-label
}

export default function NavItem({
  href,
  children,
  isActive = false,
  ...props
}: NavItemProps) {
  return (
    <UnstyledButton
      component="a"
      href={href}
      className={`modern-nav-item ${isActive ? 'active' : ''}`}
      style={{
        flex: 1,
        height: 48,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        gap: "var(--spacing-xs)",
        fontWeight: 500,
        fontSize: "12px",
        color: isActive ? "var(--color-primary-600)" : "var(--color-neutral-600)",
        textDecoration: "none",
        borderRadius: "var(--radius-md)",
        margin: "var(--spacing-xs)",
        padding: "var(--spacing-xs)",
        transition: "all var(--transition-fast)",
        position: "relative",
        background: isActive ? "var(--color-primary-50)" : "transparent",
      }}
      styles={{
        root: {
          '&:hover': {
            color: 'var(--color-primary-600)',
            backgroundColor: 'var(--color-primary-50)',
            transform: 'translateY(-1px)',
          },
          '&:active': {
            transform: 'translateY(0)',
          },
          '&:focus': {
            outline: '2px solid var(--color-primary-300)',
            outlineOffset: '2px',
          },
        },
      }}
      {...props}
    >
      {children}

      {/* Active indicator */}
      {isActive && (
        <div
          style={{
            position: "absolute",
            bottom: -2,
            left: "50%",
            transform: "translateX(-50%)",
            width: 24,
            height: 3,
            background: "var(--color-primary-500)",
            borderRadius: "2px",
            transition: "all var(--transition-fast)",
          }}
        />
      )}
    </UnstyledButton>
  );
}
