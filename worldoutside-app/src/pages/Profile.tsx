
import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, Stack, Title, Text, Group, Loader, Center, Alert, Badge, Avatar, Textarea, FileInput, Button } from "@mantine/core";
import { IconPhoto, Icon<PERSON>heck, IconUser } from "@tabler/icons-react";
import { getProfileStats } from "../actions/getProfileStats";
import { getProfileBadges } from "../actions/getProfileBadges";
import { getProfileOptions, updateProfile } from "../actions/profile";

export default function Profile() {
  const { data: stats, isPending: statsPending, error: statsError } = useQuery({
    queryKey: ["profile/stats"],
    queryFn: getProfileStats,
  });
  const { data: badgesData, isPending: badgesPending, error: badgesError } = useQuery({
    queryKey: ["profile/badges"],
    queryFn: getProfileBadges,
  });
  const { data: profile, isPending: profilePending, error: profileError, refetch: refetchProfile } = useQuery(getProfileOptions());
  // Helper pro asset URL (obrázky)
  function getAssetUrl(path: string) {
    const assetUrl = process.env.REACT_APP_ASSET_URL || (process.env.REACT_APP_API_URL ? process.env.REACT_APP_API_URL.replace(/\/api$/, '') : '');
    return `${assetUrl}/storage/${path}`;
  }

  const mutation = useMutation({
    mutationFn: updateProfile,
    onSuccess: (data) => {
      refetchProfile();
      if (data && data.profile_image) {
        setPreview(getAssetUrl(data.profile_image));
        setFile(null);
      }
    },
  });

  const [bio, setBio] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  // Set initial bio and preview when profile loads
  React.useEffect(() => {
    if (profile) {
      setBio(profile.bio || "");
      setPreview(profile.profile_image ? getAssetUrl(profile.profile_image) : null);
    }
  }, [profile]);

  function handleFileChange(f: File | null) {
    setFile(f);
    if (f) {
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target?.result as string);
      reader.readAsDataURL(f);
    } else {
      setPreview(profile?.profile_image ? getAssetUrl(profile.profile_image) : null);
    }
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    const formData = new FormData();
    if (bio) formData.append("bio", bio);
    if (file) formData.append("profile_image", file);
    mutation.mutate(formData);
  }

  if (statsPending || badgesPending || profilePending) {
    return (
      <Center style={{ minHeight: 200 }}>
        <Loader />
      </Center>
    );
  }

  return (
    <div
      style={{
        maxWidth: 480,
        margin: "0 auto",
        width: "100%",
        padding: "var(--spacing-lg)",
        minHeight: "100vh",
        background: "var(--color-neutral-50)",
        // Remove overflowY here, let MainLayout handle scrolling
      }}
    >
      <Stack align="center" gap="xl">
        {/* Modern page header */}
        <div style={{ textAlign: "center", marginBottom: "var(--spacing-lg)" }}>
          <Title
            order={1}
            style={{
              fontSize: "28px",
              fontWeight: 700,
              color: "var(--color-neutral-800)",
              marginBottom: "var(--spacing-xs)",
            }}
          >
            Môj profil
          </Title>
          <Text
            size="md"
            style={{
              color: "var(--color-neutral-600)",
              fontSize: "16px",
            }}
          >
            Spravujte svoj profil a sledujte pokrok
          </Text>
        </div>

        {/* Error alerts with modern styling */}
        {statsError && (
          <Alert
            color="red"
            title="Chyba pri načítaní štatistík"
            w="100%"
            radius="lg"
            styles={{
              root: {
                border: "1px solid var(--color-error-200)",
                backgroundColor: "var(--color-error-50)",
              },
            }}
          >
            {statsError.message}
          </Alert>
        )}
        {badgesError && (
          <Alert
            color="red"
            title="Chyba pri načítaní odznakov"
            w="100%"
            radius="lg"
            styles={{
              root: {
                border: "1px solid var(--color-error-200)",
                backgroundColor: "var(--color-error-50)",
              },
            }}
          >
            {badgesError.message}
          </Alert>
        )}
        {profileError && (
          <Alert
            color="red"
            title="Chyba pri načítaní profilu"
            w="100%"
            radius="lg"
            styles={{
              root: {
                border: "1px solid var(--color-error-200)",
                backgroundColor: "var(--color-error-50)",
              },
            }}
          >
            {profileError.message}
          </Alert>
        )}

        {/* Modern profile form */}
        <Card
          className="modern-card"
          shadow="lg"
          padding="xl"
          radius="xl"
          w="100%"
          style={{
            background: "white",
            border: "1px solid var(--color-neutral-200)",
          }}
        >
          <form onSubmit={handleSubmit}>
            <Stack gap="lg" align="center">
              {/* Profile avatar section */}
              <div style={{ textAlign: "center" }}>
                <div
                  style={{
                    position: "relative",
                    display: "inline-block",
                    marginBottom: "var(--spacing-md)",
                  }}
                >
                  <Avatar
                    src={preview}
                    size={120}
                    radius="50%"
                    style={{
                      border: "4px solid var(--color-primary-200)",
                      boxShadow: "var(--shadow-lg)",
                    }}
                  />
                  <div
                    style={{
                      position: "absolute",
                      bottom: 8,
                      right: 8,
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      background: "var(--color-primary-500)",
                      border: "3px solid white",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      transition: "all var(--transition-fast)",
                    }}
                  >
                    <IconPhoto size={16} color="white" />
                  </div>
                </div>
                <Text size="sm" style={{ color: "var(--color-neutral-600)" }}>
                  Kliknite pre zmenu profilovej fotky
                </Text>
              </div>

              <FileInput
                label="Profilový obrázok"
                description="Nahrajte novú profilovú fotku (JPG, PNG)"
                accept="image/*"
                value={file}
                onChange={handleFileChange}
                clearable
                w="100%"
                radius="lg"
                leftSection={<IconPhoto size={18} />}
                styles={{
                  input: {
                    border: "2px solid var(--color-neutral-200)",
                    backgroundColor: "var(--color-neutral-50)",
                    transition: "all var(--transition-fast)",
                    "&:focus": {
                      borderColor: "var(--color-primary-400)",
                      backgroundColor: "white",
                      boxShadow: "0 0 0 3px var(--color-primary-100)",
                    },
                  },
                  label: {
                    fontSize: "14px",
                    fontWeight: 600,
                    color: "var(--color-neutral-700)",
                  },
                }}
              />

              <Textarea
                label="Bio / Popis používateľa"
                description="Krátky popis o vás (max 500 znakov)"
                value={bio}
                onChange={(e) => setBio(e.currentTarget.value)}
                maxLength={500}
                minRows={4}
                w="100%"
                radius="lg"
                styles={{
                  input: {
                    border: "2px solid var(--color-neutral-200)",
                    backgroundColor: "var(--color-neutral-50)",
                    transition: "all var(--transition-fast)",
                    "&:focus": {
                      borderColor: "var(--color-primary-400)",
                      backgroundColor: "white",
                      boxShadow: "0 0 0 3px var(--color-primary-100)",
                    },
                  },
                  label: {
                    fontSize: "14px",
                    fontWeight: 600,
                    color: "var(--color-neutral-700)",
                  },
                }}
              />

              <Button
                type="submit"
                loading={mutation.isPending}
                w="100%"
                size="md"
                radius="lg"
                leftSection={<IconCheck size={18} />}
                styles={{
                  root: {
                    background: "linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))",
                    border: "none",
                    fontWeight: 600,
                    fontSize: "16px",
                    height: "48px",
                    transition: "all var(--transition-fast)",
                    "&:hover": {
                      background: "linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))",
                      transform: "translateY(-2px)",
                      boxShadow: "var(--shadow-lg)",
                    },
                    "&:active": {
                      transform: "translateY(0)",
                    },
                  },
                }}
              >
                Uložiť profil
              </Button>
            </Stack>
          </form>
        </Card>

        {/* Modern stats card */}
        {stats && (
          <Card
            className="modern-card"
            shadow="lg"
            padding="xl"
            radius="xl"
            w="100%"
            style={{
              background: "white",
              border: "1px solid var(--color-neutral-200)",
            }}
          >
            <Stack gap="md">
              <div style={{ textAlign: "center", marginBottom: "var(--spacing-md)" }}>
                <Text
                  size="lg"
                  fw={700}
                  style={{
                    color: "var(--color-neutral-800)",
                    marginBottom: "var(--spacing-xs)",
                  }}
                >
                  Vaše štatistiky
                </Text>
                <Text size="sm" style={{ color: "var(--color-neutral-600)" }}>
                  Prehľad vašich úspechov
                </Text>
              </div>

              <Group justify="space-between" align="center">
                <Group gap="sm" align="center">
                  <div
                    style={{
                      padding: "var(--spacing-sm)",
                      borderRadius: "var(--radius-lg)",
                      background: "var(--color-primary-100)",
                      color: "var(--color-primary-600)",
                    }}
                  >
                    🏔️
                  </div>
                  <div>
                    <Text size="sm" style={{ color: "var(--color-neutral-600)" }}>
                      Počet vrcholov
                    </Text>
                    <Text fw={700} size="lg" style={{ color: "var(--color-neutral-800)" }}>
                      {stats.peaks_count}
                    </Text>
                  </div>
                </Group>
              </Group>

              <Group justify="space-between" align="center">
                <Group gap="sm" align="center">
                  <div
                    style={{
                      padding: "var(--spacing-sm)",
                      borderRadius: "var(--radius-lg)",
                      background: "var(--color-success-100)",
                      color: "var(--color-success-600)",
                    }}
                  >
                    📈
                  </div>
                  <div>
                    <Text size="sm" style={{ color: "var(--color-neutral-600)" }}>
                      Celková nastúpaná výška
                    </Text>
                    <Text fw={700} size="lg" style={{ color: "var(--color-neutral-800)" }}>
                      {stats.total_elevation} m
                    </Text>
                  </div>
                </Group>
              </Group>
            </Stack>
          </Card>
        )}

        {/* Modern badges section */}
        <Card
          className="modern-card"
          shadow="lg"
          padding="xl"
          radius="xl"
          w="100%"
          style={{
            background: "white",
            border: "1px solid var(--color-neutral-200)",
          }}
        >
          <Stack gap="lg">
            <div style={{ textAlign: "center" }}>
              <Text
                size="lg"
                fw={700}
                style={{
                  color: "var(--color-neutral-800)",
                  marginBottom: "var(--spacing-xs)",
                }}
              >
                Získané odznaky
              </Text>
              <Text size="sm" style={{ color: "var(--color-neutral-600)" }}>
                Vaše úspechy a míľniky
              </Text>
            </div>

            <Group gap="md" w="100%" justify="center">
              {badgesData && badgesData.badges.length > 0 ? (
                badgesData.badges.map((badge: any) => (
                  <Badge
                    key={badge.id}
                    size="xl"
                    radius="lg"
                    variant="light"
                    color="teal"
                    leftSection={badge.icon}
                    style={{
                      padding: "var(--spacing-sm) var(--spacing-lg)",
                      fontSize: "14px",
                      fontWeight: 600,
                      background: "var(--color-success-100)",
                      color: "var(--color-success-700)",
                      border: "1px solid var(--color-success-200)",
                    }}
                  >
                    {badge.name}
                  </Badge>
                ))
              ) : (
                <div
                  style={{
                    textAlign: "center",
                    padding: "var(--spacing-xl)",
                    background: "var(--color-neutral-50)",
                    borderRadius: "var(--radius-lg)",
                    border: "2px dashed var(--color-neutral-300)",
                  }}
                >
                  <Text size="lg" style={{ marginBottom: "var(--spacing-xs)" }}>
                    🏆
                  </Text>
                  <Text
                    size="sm"
                    fw={500}
                    style={{ color: "var(--color-neutral-600)" }}
                  >
                    Zatiaľ žiadne odznaky
                  </Text>
                  <Text size="xs" style={{ color: "var(--color-neutral-500)" }}>
                    Navštívte vrcholy a získajte svoje prvé odznaky!
                  </Text>
                </div>
              )}
            </Group>
          </Stack>
        </Card>
      </Stack>
    </div>
  );
}
