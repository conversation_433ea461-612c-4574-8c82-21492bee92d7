<?php

namespace App\Http\Controllers;

use App\Http\Resources\PlaceResource;
use App\Models\Place;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use JsonException;

class PlaceController extends Controller {

        /**
     * Vyhľadá vrcholy podľa názvu alebo popisu (case-insensitive, partial match)
     * GET: /places/search?query=...
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        if (!$query || strlen($query) < 2) {
            return response()->json(['data' => []]);
        }

        $results = Place::where('name', 'LIKE', "%$query%")
            ->orWhere('description', 'LIKE', "%$query%")
            ->get();

        return PlaceResource::collection($results);
    }

        /**
     * Accepts photo verification for a place visit.
     * POST: /places/photo-verification
     */
    public function photoVerification(Request $request): JsonResponse
    {
        $data = $request->validate([
            'place_id' => ['required', 'integer', 'exists:places,id'],
            'photo' => ['required', 'image'],
            'exif' => ['nullable'],
        ]);
        $user = $request->user('sanctum');
        $place = Place::find($data['place_id']);

        // Store photo
        $photoPath = $request->file('photo')->store('photo-verifications', 'public');

        // Ensure exif is stored as JSON string
        $exifValue = null;
        if (isset($data['exif'])) {
            if (is_array($data['exif']) || is_object($data['exif'])) {
                $exifValue = json_encode($data['exif']);
            } else {
                // Try to decode and re-encode to ensure valid JSON string
                $decoded = json_decode($data['exif'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $exifValue = json_encode($decoded);
                } else {
                    $exifValue = (string)$data['exif'];
                }
            }
        }

        // Save or update PlaceUser pivot
        $pivot = $user->places()->where('place_id', $place->id)->first()?->pivot;
        if ($pivot) {
            $pivot->photo = $photoPath;
            $pivot->exif = $exifValue;
            $pivot->visit_type = 'pending';
            $pivot->save();
        } else {
            $user->places()->attach($place, [
                'visit_type' => 'pending',
                'photo' => $photoPath,
                'exif' => $exifValue,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Fotka bola úspešne odoslaná na overenie.',
        ], 201);
    }

    /**
     * Accepts user coordinates and a peak (place) ID.
     * POST: /places/submit-coordinates
     */
    public function submitUserCoordinatesForPeak(Request $request): JsonResponse
    {
        $data = $request->validate([
            'latitude' => ['required', 'numeric'],
            'longitude' => ['required', 'numeric'],
            'place_id' => ['required', 'integer', 'exists:places,id'],
        ]);
        $user = $request->user('sanctum');
        $place = Place::find($data['place_id']);

        // Calculate distance between user and place
        $R = 6371e3; // metres
        $latitudeRadians = $data['latitude'] * M_PI / 180;
        $destinationLatitude = $place->latitude * M_PI / 180;
        $latitudeDifference = ($place->latitude - $data['latitude']) * M_PI / 180;
        $longitudeDifference = ($place->longitude - $data['longitude']) * M_PI / 180;

        $a = sin($latitudeDifference / 2) * sin($latitudeDifference / 2) +
            cos($latitudeRadians) * cos($destinationLatitude) *
            sin($longitudeDifference / 2) * sin($longitudeDifference / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $R * $c; // in metres

        if ($distance <= 100) {
            // Verified visit
            $pivot = $user->places()->where('place_id', $place->id)->first()?->pivot;
            if ($pivot) {
                if ($pivot->visit_type === 'verified') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Place already visited',
                        'visit_type' => 'verified',
                    ], 400);
                }
                $pivot->visit_type = 'verified';
                $pivot->save();
            } else {
                $user->places()->attach($place, ['visit_type' => 'verified']);
            }

            // Badge logic
            $awarded = app(\App\Services\BadgeService::class)->checkAndAwardBadges($user);

            return response()->json([
                'success' => true,
                'message' => 'Používateľ je v okruhu 100 metrov od vrcholu.',
                'distance_meters' => $distance,
                'visit_type' => 'verified',
                'badges_awarded' => $awarded,
            ]);
        } else {
            // Prepare for pending type in the future
            // $user->places()->attach($place, ['visit_type' => 'pending']);
            return response()->json([
                'success' => false,
                'message' => 'Používateľ nie je v okruhu 100 metrov od vrcholu.',
                'distance_meters' => $distance,
                'visit_type' => 'pending',
            ], 422);
        }
    }

    // API endpoint: get visit type for a place and current user
    public function getVisitType(Place $place): JsonResponse {
        $user = auth('sanctum')->user();
        $pivot = $place->users()->where('user_id', $user->id)->first()?->pivot;
        return response()->json([
            'visit_type' => $pivot?->visit_type,
        ]);
    }

    public function index() {
        return PlaceResource::collection( Place::all() );
    }

    public function store( Request $request ) {
        $data = $request->validate( [
            'name'        => [ 'required' ],
            'description' => [ 'required' ],
            'latitude'    => [ 'required', 'numeric' ],
            'longitude'  => [ 'required', 'numeric' ],
            'image'       => [ 'required' ],
        ] );

        return new PlaceResource( Place::create( $data ) );
    }

    public function show( Place $place ) {
        return new PlaceResource( $place );
    }

    public function update( Request $request, Place $place ) {
        $data = $request->validate( [
            'name'        => [ 'required' ],
            'description' => [ 'required' ],
            'latitude'    => [ 'required', 'numeric' ],
            'longitude'  => [ 'required', 'numeric' ],
            'image'       => [ 'required' ],
        ] );

        $place->update( $data );

        return new PlaceResource( $place );
    }

    public function destroy( Place $place ) {
        $place->delete();

        return response()->json();
    }

    public function getPlaces(Request $request): AnonymousResourceCollection {
        $user = $request->user('sanctum');

        if ($request->latitude === "0" && $request->longitude === "0") {
            return PlaceResource::collection(collect());
        }

        /**
         * Formula to calculate distance between two points:
         * const R = 6371e3; // metres
         * const φ1 = lat1 * Math.PI/180; // φ, λ in radians
         * const φ2 = lat2 * Math.PI/180;
         * const Δφ = (lat2-lat1) * Math.PI/180;
         * const Δλ = (lon2-lon1) * Math.PI/180;
         *
         * const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
         * Math.cos(φ1) * Math.cos(φ2) *
         * Math.sin(Δλ/2) * Math.sin(Δλ/2);
         * const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
         *
         * const d = R * c; // in metres
         */
        $places = Place::all()->filter(function ($place) use ($request, $user) {
            $R = 6371e3; // metres
            $latitudeRadians = $request->latitude * M_PI / 180; // φ, λ in radians
            $destinationLatitude = $place->latitude * M_PI / 180;
            $latitudeDifference = ($place->latitude - $request->latitude) * M_PI / 180;
            $longitudeDifference = ($place->longitude - $request->longitude) * M_PI / 180;

            $a = sin($latitudeDifference / 2) * sin($latitudeDifference / 2) +
                cos($latitudeRadians) * cos($destinationLatitude) *
                sin($longitudeDifference / 2) * sin($longitudeDifference / 2);
            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

            $d = $R * $c; // in metres

            if ($user) {
                $place->visitedByUser( $user );
            }

            return $d <= 10000; // 10km
        });

        return PlaceResource::collection($places);
    }

    public function getVisitedPlaces(): AnonymousResourceCollection {
        return PlaceResource::collection(auth()->user()->places);
    }

    /**
     * @throws JsonException
     */
    public function getPlaceVisit(Place $place): JsonResponse {
        $user = auth()->user();
        $pivot = $user->places()->where('place_id', $place->id)->first()?->pivot;
        if ($pivot) {
            // If already verified, do nothing
            if ($pivot->visit_type === 'verified') {
                return response()->json([
                    'message' => 'Place already visited',
                    'visit_type' => 'verified',
                ], 400);
            }
            // If soft or pending, keep the highest
            if ($pivot->visit_type !== 'soft') {
                $pivot->visit_type = 'soft';
                $pivot->save();
            }
            return response()->json([
                'message' => 'Place visit type updated',
                'visit_type' => $pivot->visit_type,
            ], 200);
        }
        // No record yet, create soft
        $user->places()->attach($place, ['visit_type' => 'soft']);
        return response()->json([
            'message' => 'Place visited successfully',
            'visit_type' => 'soft',
        ], 201);
    }

        // Get planned peaks for current user
    public function getPlannedPlaces(Request $request): AnonymousResourceCollection {
        $user = $request->user('sanctum');
        $planned = $user->places()->wherePivot('visit_type', 'planned')->get();
        return PlaceResource::collection($planned);
    }

    // Add a peak to planned
    public function addPlanned(Request $request, Place $place): JsonResponse {
        $user = $request->user('sanctum');
        $pivot = $user->places()->where('place_id', $place->id)->first()?->pivot;
        if ($pivot) {
            $pivot->visit_type = 'planned';
            $pivot->save();
        } else {
            $user->places()->attach($place, ['visit_type' => 'planned']);
        }
        return response()->json(['success' => true]);
    }

    // Remove a peak from planned
    public function removePlanned(Request $request, Place $place): JsonResponse {
        $user = $request->user('sanctum');
        // Smaž všechny záznamy s visit_type 'planned' pro daného uživatele a vrchol
        $user->places()->newPivotStatement()
            ->where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->where('visit_type', 'planned')
            ->delete();
        return response()->json(['success' => true]);
    }
}