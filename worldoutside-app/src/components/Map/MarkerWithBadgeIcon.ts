import L from "leaflet";

/**
 * Creates a custom Leaflet marker icon with a visitor count badge
 *
 * This function generates a DivIcon that combines a marker image with a visitor count badge.
 * The badge is positioned at the bottom-right of the marker and follows the modern design system.
 *
 * @param iconUrl - URL of the marker icon image
 * @param visitorCount - Number of visitors to display in the badge
 * @param iconSize - Size of the marker icon [width, height]
 * @param badgeColor - Background color of the badge (CSS custom property or color value)
 * @param badgeTextColor - Text color of the badge (CSS custom property or color value)
 * @returns Leaflet DivIcon with embedded visitor count badge
 */
export function createMarkerWithBadgeIcon({
  iconUrl,
  visitorCount,
  iconSize = [25, 41],
  badgeColor = 'var(--color-primary-600)',
  badgeTextColor = 'var(--color-neutral-50)',
}: {
  iconUrl: string;
  visitorCount: number;
  iconSize?: [number, number];
  badgeColor?: string;
  badgeTextColor?: string;
}) {
  // Calculate container dimensions to accommodate the badge
  const containerWidth = iconSize[0] + 15; // Extra space for badge overflow
  const containerHeight = iconSize[1] + 15; // Extra space for badge overflow

  // HTML structure for marker with visitor count badge (fix: outer div is container size, img is icon size)
  const markerHtml = `
    <div style="
      position: relative;
      width: ${containerWidth}px;
      height: ${containerHeight}px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
    ">
      <!-- Marker icon -->
      <img
        src="${iconUrl}"
        width="${iconSize[0]}"
        height="${iconSize[1]}"
        style="
          display: block;
          position: relative;
          z-index: 1;
          width: ${iconSize[0]}px;
          height: ${iconSize[1]}px;
        "
        alt="Map marker"
      />

      <!-- Visitor count badge -->
      <div
        class="wo-marker-badge"
        style="
          background: ${badgeColor};
          color: ${badgeTextColor};
        "
        aria-label="${visitorCount} people have visited this location"
      >
        Počet návštev: ${visitorCount}
      </div>
    </div>
  `;

  // Create and return the DivIcon
  return new L.DivIcon({
    html: markerHtml,
    className: 'custom-marker-with-badge',
    iconSize: [containerWidth, containerHeight],
    iconAnchor: [iconSize[0] / 2, iconSize[1]], // Anchor at bottom center of original icon
    popupAnchor: [0, -iconSize[1]], // Popup appears above the marker
  });
}

/**
 * Enhanced marker icon creation with visitor count badge
 *
 * This is a convenience function that creates marker icons with visitor count badges
 * using predefined styling that matches the application's design system.
 *
 * @param iconUrl - URL of the marker icon image
 * @param visitorCount - Number of visitors to display
 * @param variant - Style variant for different marker types
 * @returns Leaflet DivIcon with visitor count badge
 */
export function createEnhancedMarkerIcon(
  iconUrl: string,
  visitorCount: number,
  variant: 'default' | 'visited' | 'planned' = 'default'
) {
  // Define color schemes for different marker variants
  const colorSchemes = {
    default: {
      badgeColor: 'var(--color-primary-600)',
      badgeTextColor: 'var(--color-neutral-50)',
    },
    visited: {
      badgeColor: 'var(--color-success-600)',
      badgeTextColor: 'var(--color-neutral-50)',
    },
    planned: {
      badgeColor: 'var(--color-warning-600)',
      badgeTextColor: 'var(--color-neutral-50)',
    },
  };

  const colors = colorSchemes[variant];

  return createMarkerWithBadgeIcon({
    iconUrl,
    visitorCount,
    badgeColor: colors.badgeColor,
    badgeTextColor: colors.badgeTextColor,
  });
}