<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class LeaderboardController extends Controller
{
    /**
     * Get the leaderboard with filters and pagination.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $metric = $request->input('metric', 'peaks'); // 'peaks' or 'elevation'
        $region = $request->input('region'); // region slug or null
        $period = $request->input('period', 'all'); // 'all', 'year', 'month'
        $perPage = (int) $request->input('per_page', 10);
        $page = (int) $request->input('page', 1);

        // Build base query for users with stats
        $query = User::query()
            ->select('users.id', 'users.name', 'users.profile_image')
            ->with(['badges:badges.id,name,slug,icon'])
            ->withCount(['places as peaks_count' => function ($q) use ($region, $period) {
                $q->where('place_user.visit_type', 'verified');
                if ($region) {
                    $q->where('places.region', $region);
                }
                if ($period !== 'all') {
                    $q->where('place_user.created_at', '>=', self::periodStart($period));
                }
            }]);

        // Add elevation sum as subquery
        $query->addSelect([
            'total_elevation' => DB::table('place_user')
                ->join('places', 'places.id', '=', 'place_user.place_id')
                ->whereColumn('place_user.user_id', 'users.id')
                ->where('place_user.visit_type', 'verified')
                ->when($region, fn($q) => $q->where('places.region', $region))
                ->when($period !== 'all', fn($q) => $q->where('place_user.created_at', '>=', self::periodStart($period)))
                ->selectRaw('COALESCE(SUM(places.elevation),0)')
        ]);

        // Sorting
        if ($metric === 'elevation') {
            $query->orderByDesc('total_elevation');
        } else {
            $query->orderByDesc('peaks_count');
        }

        // Pagination
        $users = $query->paginate($perPage, ['*'], 'page', $page);

        // Get all regions for filter dropdown
        $regions = DB::table('places')->distinct()->pluck('region')->filter()->values();

        // Format response
        $currentUserId = $request->user('sanctum')?->id;
        $data = $users->getCollection()->map(function ($user) use ($currentUserId) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->profile_image,
                'peaks' => $user->peaks_count,
                'elevation' => $user->total_elevation,
                'badges' => $user->badges->map(fn($b) => [
                    'slug' => $b->slug,
                    'name' => $b->name,
                    'icon' => $b->icon,
                ]),
                'isCurrentUser' => $user->id === $currentUserId,
            ];
        });

        return response()->json([
            'data' => $data,
            'meta' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ],
            'regions' => $regions,
        ]);
    }

    /**
     * Get the start date for a period filter.
     *
     * @param string $period
     * @return string
     */
    private static function periodStart(string $period): string
    {
        if ($period === 'year') {
            return now()->startOfYear()->toDateTimeString();
        }
        if ($period === 'month') {
            return now()->startOfMonth()->toDateTimeString();
        }
        return '1970-01-01 00:00:00';
    }
}
