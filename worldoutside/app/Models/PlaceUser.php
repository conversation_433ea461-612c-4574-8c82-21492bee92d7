<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Pivot model pre vzťah medzi používateľom a miestom (návšteva)
 *
 * Tento model reprezentuje konkrétnu návštevu miesta používateľom.
 * Obsahuje dodatočné informácie o návšteve ako typ návštevy, fotografiu
 * a EXIF dáta z fotografie pre overenie autenticity návštevy.
 *
 * @property int $id Jedinečný identifikátor záznamu návštevy
 * @property int $user_id ID používateľa, ktorý navštívil miesto
 * @property int $place_id ID navštíveného miesta
 * @property string $visit_type Typ návštevy (planned/visited/verified)
 * @property string|null $photo URL fotografie z návštevy
 * @property array|null $exif EXIF dáta z fotografie (GPS súradnice, čas, atď.)
 * @property \Illuminate\Support\Carbon|null $created_at Čas vytvorenia záznamu
 * @property \Illuminate\Support\Carbon|null $updated_at Čas poslednej aktualizácie
 */
class PlaceUser extends Model {
    use HasFactory;

    /**
     * Názov tabuľky v databáze
     *
     * @var string
     */
    protected $table = 'place_user';

    /**
     * Atribúty, ktoré môžu byť hromadne priradené (mass assignment)
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',    // ID používateľa
        'place_id',   // ID miesta
        'visit_type', // Typ návštevy: 'planned', 'visited', 'verified'
        'photo',      // URL fotografie z návštevy
        'exif',       // EXIF dáta z fotografie (JSON)
    ];

    /**
     * Definuje, ako majú byť atribúty automaticky konvertované
     *
     * @var array<string, string>
     */
    protected $casts = [
        'exif' => 'array', // Automatická konverzia JSON na pole a späť
    ];

    /**
     * Vzťah s modelom User (používateľ, ktorý navštívil miesto)
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Vzťah s modelom Place (navštívené miesto)
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function place()
    {
        return $this->belongsTo(Place::class);
    }
}
