import React, { useState } from 'react';
import { 
  TextInput, 
  Select, 
  Group, 
  Paper, 
  Stack, 
  Text,
  Badge,
  ActionIcon,
  Tooltip
} from '@mantine/core';
import { IconSearch, IconX, IconFilter } from '@tabler/icons-react';

/**
 * Interface defining the structure of search and filter criteria
 * Used to filter mountain peaks based on various attributes
 */
interface SearchFilters {
  searchTerm: string;           // Text search for peak names
  difficulty: string | null;    // Difficulty level filter
  visitStatus: string | null;   // Visit status filter
  elevationRange: string | null; // Elevation range filter
}

/**
 * Props interface for the SearchFilter component
 */
interface SearchFilterProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  resultCount: number; // Number of peaks matching current filters
}

/**
 * SearchFilter Component
 * 
 * Provides search and filtering functionality for mountain peaks on the map.
 * Features:
 * - Real-time text search by peak name
 * - Filter by difficulty level
 * - Filter by visit status
 * - Filter by elevation range
 * - Shows count of filtered results
 * - Clear all filters functionality
 * 
 * @param filters Current filter state
 * @param onFiltersChange Callback to update filters
 * @param resultCount Number of peaks matching filters
 */
export default function SearchFilter({ 
  filters, 
  onFiltersChange, 
  resultCount 
}: SearchFilterProps) {
  // State for showing/hiding filters
  const [filtersOpen, setFiltersOpen] = useState(false);

  /**
   * Updates a specific filter while preserving others
   * @param key The filter key to update
   * @param value The new value for the filter
   */
  const updateFilter = (key: keyof SearchFilters, value: string | null) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  /**
   * Clears all active filters, resetting to default state
   */
  const clearAllFilters = () => {
    onFiltersChange({
      searchTerm: '',
      difficulty: null,
      visitStatus: null,
      elevationRange: null
    });
  };

  /**
   * Checks if any filters are currently active
   * @returns true if any filter has a non-default value
   */
  const hasActiveFilters = () => {
    return filters.searchTerm !== '' || 
           filters.difficulty !== null || 
           filters.visitStatus !== null || 
           filters.elevationRange !== null;
  };

  return (
    <Paper
      className="modern-search-panel"
      shadow="lg"
      p="lg"
      radius="xl"
      style={{
        position: 'absolute',
        zIndex: 2000,
        pointerEvents: 'auto',
        background: 'linear-gradient(0deg,rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.25) 50%, rgba(0, 0, 0, 0.5) 100%)',
        transition: 'all var(--transition-normal)',
      }}
      styles={{
        root: {
          '&:hover': {
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
            transform: 'translateY(-1px)',
          },
        },
      }}
    >
      <Stack gap={0}>
        {/* Modern header with title and clear button */}
        <Group justify="space-between" align="center" mb="xs">
          <Group gap="sm" align="center">
            <div
              style={{
                padding: 'var(--spacing-xs)',
                borderRadius: 'var(--radius-md)',
                background: 'var(--color-primary-100)',
                color: 'var(--color-primary-600)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <IconFilter size={18} />
            </div>
            <div>
              <Text fw={600} size="sm" style={{ color: 'var(--color-neutral-100)' }}>
                Vyhľadávanie vrcholov
              </Text>
              <Text size="xs" style={{ color: 'var(--color-neutral-200)' }}>
                Filtrujte podľa kritérií
              </Text>
            </div>
          </Group>
          {hasActiveFilters() && (
            <Tooltip
              label="Vymazať všetky filtre"
              position="bottom"
              withArrow
              radius="md"
            >
              <ActionIcon
                variant="subtle"
                color="gray"
                size="md"
                radius="md"
                onClick={clearAllFilters}
                style={{
                  transition: 'all var(--transition-fast)',
                  color: 'var(--color-neutral-500)',
                }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: 'var(--color-error-50)',
                      color: 'var(--color-error-600)',
                      transform: 'scale(1.1)',
                    },
                  },
                }}
              >
                <IconX size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>

        {/* Modern search input */}

        <TextInput
          placeholder="Vyhľadaj vrchol podľa názvu..."
          value={filters.searchTerm}
          onChange={(event) => updateFilter('searchTerm', event.currentTarget.value)}
          leftSection={
            <IconSearch
              size={18}
              style={{ color: 'var(--color-neutral-400)' }}
            />
          }
          rightSection={
            filters.searchTerm && (
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                radius="md"
                onClick={() => updateFilter('searchTerm', '')}
                style={{
                  transition: 'all var(--transition-fast)',
                  color: 'var(--color-neutral-400)',
                }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: 'var(--color-neutral-100)',
                      color: 'var(--color-neutral-600)',
                      transform: 'scale(1.1)',
                    },
                  },
                }}
              >
                <IconX size={14} />
              </ActionIcon>
            )
          }
          radius="lg"
          size="md"
          styles={{
            input: {
              border: '2px solid var(--color-neutral-200)',
              backgroundColor: 'var(--color-neutral-50)',
              transition: 'all var(--transition-fast)',
              fontSize: '14px',
              fontWeight: 500,
              '&:focus': {
                borderColor: 'var(--color-primary-400)',
                backgroundColor: 'white',
                boxShadow: '0 0 0 3px var(--color-primary-100)',
                transform: 'translateY(-1px)',
              },
              '&:hover': {
                borderColor: 'var(--color-neutral-300)',
                backgroundColor: 'white',
              },
            },
          }}
        />

        <Group justify="flex-start" mt="sm">
          <ActionIcon
            variant="dark"
            color="blue"
            onClick={() => setFiltersOpen((open: boolean) => !open)}
            title={filtersOpen ? 'Skryť filtre' : 'Zobraziť filtre'}
            aria-label={filtersOpen ? 'Skryť filtre' : 'Zobraziť filtre'}
          >
            <IconFilter size={18} />
          </ActionIcon>
          <Text size="sm" style={{ cursor: 'pointer', color: 'var(--color-neutral-50)' }} onClick={() => setFiltersOpen((open: boolean) => !open)}>
            {filtersOpen ? 'Skryť filtre' : 'Zobraziť filtre'}
          </Text>
        </Group>

        <div
          className={`filters-animated${filtersOpen ? ' open' : ''}`}
          style={{
            maxHeight: filtersOpen ? 500 : 0,
            opacity: filtersOpen ? 1 : 0,
            overflow: 'hidden',
            transition: 'max-height 0.4s cubic-bezier(0.4,0,0.2,1), opacity 0.3s ease',
            marginTop: filtersOpen ? '0.5rem' : 0,
          }}
        >
          <Stack gap="xs">
            <Select
              label="Stav návštevy"
              placeholder="Vyberte stav návštevy"
              value={filters.visitStatus}
              onChange={(value) => updateFilter('visitStatus', value)}
              data={[
                { value: 'verified', label: '✅ Overené' },
                { value: 'soft', label: '📍 Neoverená návšteva' },
                { value: 'planned', label: '📋 Plánované' },
                { value: 'pending', label: '⏳ Čaká na overenie' },
                { value: 'unvisited', label: '⭕ Nenavštívené' }
              ]}
              clearable
              radius="sm"
              size="xs"
              styles={{
                input: {
                  border: '1px solid var(--color-neutral-200)',
                  backgroundColor: 'var(--color-neutral-50)',
                  transition: 'all var(--transition-fast)',
                  fontSize: '11px',
                  fontWeight: 500,
                  color: 'var(--color-neutral-900)',
                  padding: '3px 6px',
                  minHeight: '24px',
                },
                label: {
                  fontSize: '11px',
                  fontWeight: 600,
                  color: 'var(--color-neutral-100)',
                  marginBottom: 'var(--spacing-xs)',
                },
              }}
            />
            <Select
              label="Výškové rozpätie"
              placeholder="Vyberte výškové rozpätie"
              value={filters.elevationRange}
              onChange={(value) => updateFilter('elevationRange', value)}
              data={[
                { value: '0-500', label: 'Pod 500 m' },
                { value: '500-1000', label: '500 m - 1000 m' },
                { value: '1000-1500', label: '1000 m - 1500 m' },
                { value: '1500-2000', label: '1500 m - 2000 m' },
                { value: '2000+', label: 'Nad 2000 m' }
              ]}
              clearable
              radius="sm"
              size="xs"
              styles={{
                input: {
                  border: '1px solid var(--color-neutral-200)',
                  backgroundColor: 'var(--color-neutral-50)',
                  transition: 'all var(--transition-fast)',
                  fontSize: '11px',
                  fontWeight: 500,
                  color: 'var(--color-neutral-900)',
                  padding: '3px 6px',
                  minHeight: '24px',
                },
                label: {
                  fontSize: '11px',
                  fontWeight: 600,
                  color: 'var(--color-neutral-100)',
                  marginBottom: 'var(--spacing-xs)',
                },
              }}
            />
          </Stack>
        </div>

        {/* Modern results count display */}
        <Group justify="space-between" align="center" mt="sm">
          <Badge
            variant="light"
            color={resultCount > 0 ? "blue" : "gray"}
            size="md"
            radius="lg"
            style={{
              fontWeight: 600,
              fontSize: '12px',
              padding: 'var(--spacing-xs) var(--spacing-sm)',
              background: resultCount > 0 ? 'var(--color-primary-100)' : 'var(--color-neutral-100)',
              color: resultCount > 0 ? 'var(--color-primary-700)' : 'var(--color-neutral-600)',
              border: `1px solid ${resultCount > 0 ? 'var(--color-primary-200)' : 'var(--color-neutral-200)'}`,
            }}
          >
            {resultCount} vrchol{resultCount === 1 ? '' : (resultCount >= 2 && resultCount <= 4 ? 'y' : 'ov')} nájden{resultCount === 1 ? 'ý' : 'é'}
          </Badge>

          {hasActiveFilters() && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 'var(--spacing-xs)',
                padding: 'var(--spacing-xs) var(--spacing-sm)',
                borderRadius: 'var(--radius-lg)',
                background: 'var(--color-warning-50)',
                border: '1px solid var(--color-warning-200)',
              }}
            >
              <div
                style={{
                  width: '6px',
                  height: '6px',
                  borderRadius: '50%',
                  background: 'var(--color-warning-500)',
                  animation: 'pulse 2s infinite',
                }}
              />
              <Text
                size="xs"
                style={{
                  color: 'var(--color-warning-700)',
                  fontWeight: 500,
                  fontSize: '11px',
                }}
              >
                Filtrovanie aktívne
              </Text>
            </div>
          )}
        </Group>
      </Stack>
    </Paper>
  );
}

/**
 * Utility function to filter places based on search criteria
 * 
 * @param places Array of places to filter
 * @param filters Current filter criteria
 * @returns Filtered array of places
 */
export function filterPlaces(places: Place[], filters: SearchFilters): Place[] {
  return places.filter(place => {
    // Text search filter - case insensitive search in name and description
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const nameMatch = place.name.toLowerCase().includes(searchLower);
      const descMatch = place.description.toLowerCase().includes(searchLower);
      if (!nameMatch && !descMatch) return false;
    }

    // Visit status filter
    if (filters.visitStatus) {
      if (filters.visitStatus === 'unvisited') {
        // Show only unvisited places (no visit_type or visited = false)
        if (place.visit_type || place.visited) return false;
      } else {
        // Show places with specific visit status
        if (place.visit_type !== filters.visitStatus) return false;
      }
    }

    // Elevation range filter
    if (filters.elevationRange && place.elevation !== undefined) {
      const elevation = place.elevation;
      switch (filters.elevationRange) {
        case '0-500':
          if (elevation >= 500) return false;
          break;
        case '500-1000':
          if (elevation < 500 || elevation >= 1000) return false;
          break;
        case '1000-1500':
          if (elevation < 1000 || elevation >= 1500) return false;
          break;
        case '1500-2000':
          if (elevation < 1500 || elevation >= 2000) return false;
          break;
        case '2000+':
          if (elevation < 2000) return false;
          break;
      }
    }

    return true;
  });
}
