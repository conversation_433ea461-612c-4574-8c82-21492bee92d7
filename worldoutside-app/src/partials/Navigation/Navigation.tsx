
import NavItem from "./NavItem";
import { Group, ActionIcon, Tooltip } from "@mantine/core";
import { IconHome, IconMapPin, IconLogout, IconUser, IconTrophy } from "@tabler/icons-react";
import { useNavigate, useLocation } from "react-router-dom";
import { success } from "../../misc/notif";

/**
 * Navigation Component
 *
 * Modern bottom navigation bar with:
 * - Clean, minimalist design with subtle hover effects
 * - Active state indicators with visual feedback
 * - Smooth transitions and micro-interactions
 * - Accessible design with proper ARIA labels
 * - Responsive layout that works on all screen sizes
 *
 * Features:
 * - Home, Visited Places, and Profile navigation
 * - Logout functionality with confirmation
 * - Visual active state indicators
 * - Hover effects and smooth transitions
 */

function Navigation() {
  const navigate = useNavigate();
  const location = useLocation();

  function handleLogout() {
    localStorage.removeItem("wo-token");
    success({ title: "Odhlásenie", message: "<PERSON><PERSON> ste <PERSON>." });
    navigate("/prihlasenie");
  }

  return (
    <Group
      justify="space-between"
      gap={0}
      style={{
        height: 64,
        padding: "var(--spacing-xs) var(--spacing-md)",
      }}
    >
      {/* Home navigation item */}
      <NavItem
        href="/"
        isActive={location.pathname === "/"}
        aria-label="Domovská stránka"
      >
        <IconHome size={20} />
        <span>Domov</span>
      </NavItem>

      {/* Visited places navigation item */}
      <NavItem
        href="/navstivene"
        isActive={location.pathname === "/navstivene"}
        aria-label="Navštívené miesta"
      >
        <IconMapPin size={20} />
        <span>Navštívené</span>
      </NavItem>


      {/* Leaderboard navigation item */}
      <NavItem
        href="/leaderboard"
        isActive={location.pathname === "/leaderboard"}
        aria-label="Leaderboard"
      >
        <IconTrophy size={20} />
        <span>Leaderboard</span>
      </NavItem>

      {/* Profile navigation item */}
      <NavItem
        href="/profil"
        isActive={location.pathname === "/profil"}
        aria-label="Profil používateľa"
      >
        <IconUser size={20} />
        <span>Profil</span>
      </NavItem>

      {/* Logout button with modern styling */}
      <Tooltip
        label="Odhlásiť sa"
        position="top"
        withArrow
        radius="md"
        style={{
          fontSize: "14px",
          fontWeight: 500,
        }}
      >
        <ActionIcon
          onClick={handleLogout}
          variant="subtle"
          color="red"
          size="lg"
          radius="md"
          style={{
            marginLeft: "var(--spacing-sm)",
            transition: "all var(--transition-fast)",
            color: "var(--color-neutral-600)",
          }}
          styles={{
            root: {
              '&:hover': {
                backgroundColor: 'var(--color-error-50)',
                color: 'var(--color-error-600)',
                transform: 'translateY(-1px)',
              },
              '&:active': {
                transform: 'translateY(0)',
              },
            },
          }}
          aria-label="Odhlásiť sa z aplikácie"
        >
          <IconLogout size={20} />
        </ActionIcon>
      </Tooltip>
    </Group>
  );
}

export default Navigation;
