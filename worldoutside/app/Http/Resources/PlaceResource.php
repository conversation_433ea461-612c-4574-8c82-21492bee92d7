<?php

namespace App\Http\Resources;

use App\Models\Place;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Place */
class PlaceResource extends JsonResource {
	public function toArray( Request $request ): array {
		return [
			'created_at'  => $this->created_at,
			'updated_at'  => $this->updated_at,
			'id'          => $this->id,
			'name'        => $this->name,
			'description' => $this->description,
			'latitude'    => $this->latitude,
			'longitude'  => $this->longitude,
			'image'       => $this->image,
			'visited'     => $this->visited,
			'visitor_count' => $this->users()->wherePivot('visit_type', 'verified')->count(),
			'visit_type'  => $this->when(
				auth('sanctum')->check(),
				function () {
					$user = auth('sanctum')->user();
					$pivots = $this->users()->where('user_id', $user->id)->get()->pluck('pivot');
					// Priority: verified > soft > pending
				   // Priority: verified > soft > pending > planned
				   $types = $pivots->pluck('visit_type')->toArray();
				   if (in_array('verified', $types)) return 'verified';
				   if (in_array('soft', $types)) return 'soft';
				   if (in_array('pending', $types)) return 'pending';
				   if (in_array('planned', $types)) return 'planned';
				   return null;
				}
			),
		];
	}
}
