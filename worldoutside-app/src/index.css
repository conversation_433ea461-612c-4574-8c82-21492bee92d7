/* Import Google Fonts for modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Import Mantine styles */
@import "@mantine/core/styles.css";
@import "@mantine/notifications/styles.css";

/* Import Tailwind */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern CSS Custom Properties for Design System */
:root {
  /* Color Palette - Modern, accessible colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* Neutral colors */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  /* Success colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;

  /* Warning colors */
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;

  /* Error colors */
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;

  /* Spacing system */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: var(--color-neutral-800);
  background-color: var(--color-neutral-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Leaflet map controls - Modern styling */
.leaflet-control-zoom {
  left: var(--spacing-md) !important;
  right: auto !important;
  bottom: var(--spacing-md) !important;
  top: auto !important;
  position: absolute !important;
  z-index: 2001 !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  border: none !important;
  overflow: hidden;
}

.leaflet-control-zoom a {
  background-color: white !important;
  border: none !important;
  color: var(--color-neutral-700) !important;
  transition: all var(--transition-fast) !important;
  font-weight: 500 !important;
}

.leaflet-control-zoom a:hover {
  background-color: var(--color-primary-50) !important;
  color: var(--color-primary-600) !important;
  transform: scale(1.05);
}

.leaflet-top, .leaflet-right {
  pointer-events: none;
}

.leaflet-bottom .leaflet-control-zoom {
  left: var(--spacing-md) !important;
  right: auto !important;
  bottom: var(--spacing-md) !important;
  top: auto !important;
  position: absolute !important;
  z-index: 2001 !important;
}

/* Mantine component overrides */
.mantine-Select-dropdown {
  z-index: 3000 !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-neutral-200) !important;
}

.leaflet-container {
  z-index: 0 !important;
}

/* Modern cluster marker styles */
.cluster-marker {
  background: var(--color-primary-500);
  border: 3px solid white;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  font-family: var(--font-family-sans);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.cluster-marker:hover {
  transform: scale(1.15);
  box-shadow: var(--shadow-xl);
  border-width: 4px;
}

.cluster-small {
  width: 32px;
  height: 32px;
  background: var(--color-success-500);
  font-size: 11px;
}

.cluster-medium {
  width: 38px;
  height: 38px;
  background: var(--color-warning-500);
  font-size: 12px;
}

.cluster-large {
  width: 44px;
  height: 44px;
  background: var(--color-error-500);
  font-size: 13px;
  font-weight: 700;
}

.custom-cluster-icon {
  background: transparent !important;
  border: none !important;
}

/* Visitor count badge for map markers */
.wo-marker-badge {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  bottom: -10%;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 2px 7px;
  font-size: 11px;
  font-weight: 600;
  font-family: var(--font-family-sans);
  min-width: 18px;
  min-height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  border: 2px solid var(--color-neutral-50);
  pointer-events: none;
  user-select: none;
  line-height: 1;
  display: inline-flex;
  text-wrap: nowrap;
  
}

.wo-marker-badge:hover {
  transform: translate(40%, 40%) scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Custom marker with badge container */
.custom-marker-with-badge {
  background: transparent !important;
  border: none !important;
  overflow: visible !important;
}

/* Responsive adjustments for visitor count badges */
@media (max-width: 768px) {
  .wo-marker-badge {
    font-size: 10px;
    min-width: 18px;
    min-height: 18px;
    padding: 1px 5px;
  }
}

/* Modern component styles */
.modern-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--color-primary-200);
}

.modern-button {
  background: var(--color-primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 500;
  font-family: var(--font-family-sans);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modern-button:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-button:active {
  transform: translateY(0);
}

.modern-button:focus {
  outline: 2px solid var(--color-primary-300);
  outline-offset: 2px;
}

/* Modern navigation styles */
.modern-nav {
  background: white;
  border-top: 1px solid var(--color-neutral-200);
  box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.1);
  backdrop-filter: blur(8px);
}

.modern-nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  color: var(--color-neutral-600);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  margin: var(--spacing-xs);
  position: relative;
}

.modern-nav-item:hover {
  color: var(--color-primary-600);
  background: var(--color-primary-50);
  transform: translateY(-1px);
}

.modern-nav-item.active {
  color: var(--color-primary-600);
  background: var(--color-primary-100);
}

.modern-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: var(--color-primary-500);
  border-radius: 2px;
}

/* Modern form styles */
.modern-input {
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-sans);
  transition: all var(--transition-fast);
  background: white;
}

.modern-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.modern-input:hover {
  border-color: var(--color-neutral-400);
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Modern search panel styles */
.modern-search-panel {
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.modern-search-panel:hover {
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
}

/* Modern popup styles */
.modern-popup {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Mantine component overrides */
.mantine-Modal-content {
  border-radius: var(--radius-xl) !important;
  border: 1px solid var(--color-neutral-200) !important;
  box-shadow: var(--shadow-xl) !important;
}

.mantine-Modal-header {
  border-bottom: 1px solid var(--color-neutral-200) !important;
  padding-bottom: var(--spacing-md) !important;
  margin-bottom: var(--spacing-lg) !important;
}

.mantine-Alert-root {
  border-radius: var(--radius-lg) !important;
}

.mantine-Progress-root {
  background-color: var(--color-neutral-200) !important;
  border-radius: var(--radius-lg) !important;
}

.mantine-FileInput-input {
  transition: all var(--transition-fast) !important;
}

.mantine-FileInput-input:hover {
  border-color: var(--color-primary-400) !important;
  background-color: var(--color-primary-50) !important;
}

/* Leaflet popup overrides */
.leaflet-popup-content-wrapper {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 0px!important;
  padding: 0 !important;
  background: transparent !important;
}

.leaflet-popup-content {
  margin: 0 !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden;
}

.leaflet-popup-tip {
  background: rgba(255,255,255,0.15) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255,255,255,0.25) !important;
  box-shadow: var(--shadow-md) !important;
}

/* Enhanced button styles */
.modern-button-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  border: none;
  color: white;
  font-weight: 600;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.modern-button-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-button-secondary {
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-700);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.modern-button-secondary:hover {
  background: var(--color-neutral-200);
  border-color: var(--color-neutral-400);
  transform: translateY(-1px);
}

/* Responsive utilities */
@media (max-width: 768px) {
  :root {
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
  }

  .modern-nav-item {
    font-size: 12px;
    padding: var(--spacing-sm);
  }

  .modern-search-panel {
    minWidth: 300px !important;
    maxWidth: 350px !important;
    top: var(--spacing-sm) !important;
    left: var(--spacing-sm) !important;
  }

  .modern-popup {
    minWidth: 280px !important;
    maxWidth: 320px !important;
  }

  .cluster-small {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .cluster-medium {
    width: 32px;
    height: 32px;
    font-size: 11px;
  }

  .cluster-large {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }
}

/* White close button for Leaflet popup */
.leaflet-popup-close-button {
  color: white !important;
  z-index: 2002 !important;
}