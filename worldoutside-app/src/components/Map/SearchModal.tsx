import React, { useRef, useEffect } from 'react';
import { Paper } from '@mantine/core';
import SearchFilter from './SearchFilter';

interface SearchModalProps {
  opened: boolean;
  onClose: () => void;
  filters: any;
  onFiltersChange: (filters: any) => void;
  resultCount: number;
}

export default function SearchModal({ opened, onClose, filters, onFiltersChange, resultCount }: SearchModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    }
    if (opened) {
      document.addEventListener('mousedown', handleClick);
    }
    return () => {
      document.removeEventListener('mousedown', handleClick);
    };
  }, [opened, onClose]);

  if (!opened) return null;

  return (
    <div
      ref={modalRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100vw',
        zIndex: 3000,
        background: 'transparent',
        borderRadius: 0,
        minWidth: 0,
        maxWidth: '100vw',
        padding: 0,
        display: 'flex',
        justifyContent: 'center',
        transition: 'all 0.2s',
        backdropFilter: 'none',
        boxShadow: 'none',
      }}
    >
      <div style={{ width: '100%', maxWidth: 500 }}>
        <SearchFilter
          filters={filters}
          onFiltersChange={onFiltersChange}
          resultCount={resultCount}
        />
      </div>
    </div>
  );
}
