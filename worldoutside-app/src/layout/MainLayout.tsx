
import React from "react";
import { Paper } from "@mantine/core";
import Navigation from "../partials/Navigation/Navigation";

/**
 * MainLayout Component
 *
 * Provides the main layout structure for the WorldOutside application.
 * Features a modern, clean design with:
 * - Full-height layout with flexible main content area
 * - Modern navigation bar with subtle shadows and backdrop blur
 * - Responsive design that adapts to different screen sizes
 * - Consistent spacing and modern visual hierarchy
 *
 * @param children - The main content to be rendered in the layout
 * @returns JSX.Element - The complete layout structure
 */

type MainLayoutProps = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        minHeight: "100vh",
        height: "100vh",
        background: "var(--color-neutral-50)",
      }}
    >
      {/* Main content area with modern styling */}
      <main
        style={{
          flex: 1,
          overflow: "auto",
          background: "white",
          boxShadow: "var(--shadow-sm)",
        }}
      >
        {children}
      </main>

      {/* Modern navigation bar */}
      <Paper
        className="modern-nav"
        shadow="lg"
        radius={0}
        p={0}
        withBorder={false}
        style={{
          borderTop: "1px solid var(--color-neutral-200)",
          height: 64,
          background: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(8px)",
          boxShadow: "0 -4px 6px -1px rgb(0 0 0 / 0.1), 0 -2px 4px -2px rgb(0 0 0 / 0.1)",
          position: "sticky",
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 100,
        }}
      >
        <Navigation />
      </Paper>
    </div>
  );
}
