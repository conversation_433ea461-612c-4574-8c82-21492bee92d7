import { QueryFunctionContext, queryOptions } from "@tanstack/react-query";

/**
 * Vytvorí query options pre načítanie miest v okolí zadaných GPS súradníc
 *
 * Táto funkcia pripravuje konfiguráciu pre React Query na načítanie
 * zoznamu miest (vrcholov) v blízkosti zadanej GPS pozície.
 *
 * @param latitude Zemepisná šírka stredu oblasti vyhľadávania
 * @param longitude Zemepisná dĺžka stredu oblasti vyhľadávania
 * @returns Query options objekt pre React Query
 */
export function getPlacesOptions(latitude: number, longitude: number) {
  return queryOptions({
    queryKey: ["places", latitude, longitude], // Jedinečný kľúč pre cache
    queryFn: getPlaces,                        // Funkcia pre načítanie dát
  });
}

/**
 * Načíta zoznam miest z API na základe GPS súradníc
 *
 * Táto funkcia vykonáva HTTP požiadavku na backend API pre získanie
 * zoznamu miest (vrcholov) v okolí zadaných GPS súradníc. Zahŕňa
 * autentifikáciu pomocou Bearer tokenu.
 *
 * @param queryKey Pole obsahujúce kľúč query a GPS súradnice
 * @returns Promise s poľom objektov Place
 * @throws Error ak sa nepodarí načítať dáta z API
 */
async function getPlaces({
  queryKey,
}: QueryFunctionContext<[string, number, number]>): Promise<Place[]> {
  // Extrakcia GPS súradníc z query kľúča
  const [_, latitude, longitude] = queryKey;

  // HTTP požiadavka na backend API
  const response = await fetch(
    `${process.env.REACT_APP_API_URL}/places/${latitude}/${longitude}`,
    {
      headers: {
        Accept: "application/json",
        // Autentifikácia pomocou Bearer tokenu z localStorage
        Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      },
    },
  );

  // Kontrola úspešnosti požiadavky
  if (!response.ok) {
    throw new Error("Nepodarilo sa získať dáta");
  }

  // Parsovanie JSON odpovede
  const data = (await response.json()) as { data: Place[] };

  // Vrátenie pole miest z API odpovede
  return data.data;
}
