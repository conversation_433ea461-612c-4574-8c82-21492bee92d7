<?php

namespace App\Observers;

use App\Models\PlaceUser;
use App\Services\BadgeService;

/**
 * Observer pre model PlaceUser
 *
 * Tento observer sleduje zmeny v modeli PlaceUser (návštevy miest)
 * a automaticky spúšťa kontrolu a udeľovanie odznakov používateľom
 * po overení návštevy miesta.
 */
class PlaceUserObserver
{
    /**
     * Handler pre udalosť uloženia záznamu PlaceUser
     *
     * Táto metóda sa automaticky volá po každom uložení záznamu
     * návštevy miesta (vytvorenie nového alebo aktualizácia existujúceho).
     * Ak je návšteva typu 'verified', spustí kontrolu odznakov.
     *
     * @param PlaceUser $placeUser Uložený záznam návštevy miesta
     * @return void
     */
    public function saved(PlaceUser $placeUser)
    {
        // Kontrola odznakov len pre overené návštevy
        // Odznaky sa udeľujú len za skutočne overené návštevy, nie za plánované
        if ($placeUser->visit_type === 'verified') {
            // Získanie používateľa spojeného s návštevou
            $user = $placeUser->user;

            // Ak používateľ existuje, spustíme kontrolu a udeľovanie odznakov
            if ($user) {
                // Vytvorenie inštancie BadgeService a spustenie kontroly odznakov
                app(BadgeService::class)->checkAndAwardBadges($user);
            }
        }
    }
}
