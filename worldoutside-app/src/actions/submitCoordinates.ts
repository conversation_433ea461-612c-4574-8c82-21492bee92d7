/**
 * Odošle GPS súradnice na backend pre overenie návštevy miesta
 *
 * Táto funkcia odošle aktuálne GPS súradnice používateľa na backend API
 * pre overenie, že sa používateľ skutočne nachádza v blízkosti daného miesta.
 * Backend porovná odoslané súradnice s pozíciou miesta a rozhodne, či je
 * návšteva platná (používateľ je dostatočne blízko).
 *
 * @param placeId ID miesta, ktoré sa má overiť
 * @param latitude Aktuálna zemepisná šírka používateľa
 * @param longitude Aktuálna zemepisná dĺžka používateľa
 * @returns Promise s odpoveďou z API (obsahuje informácie o úspešnosti overenia)
 * @throws Error ak sa nepodarí overiť návštevu (napr. pr<PERSON><PERSON><PERSON>ale<PERSON> od miesta)
 */
export async function submitCoordinates(placeId: number, latitude: number, longitude: number): Promise<any> {
  // HTTP POST požiadavka na backend API endpoint pre overenie súradníc
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places/submit-coordinates`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      // Autentifikácia pomocou Bearer tokenu z localStorage
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
    // Odoslanie dát v JSON formáte
    body: JSON.stringify({
      place_id: placeId,  // ID miesta na overenie
      latitude,           // Aktuálna GPS šírka používateľa
      longitude,          // Aktuálna GPS dĺžka používateľa
    }),
  });

  // Kontrola úspešnosti požiadavky
  if (!response.ok) {
    throw new Error("Nepodarilo sa overiť návštevu");
  }

  // Vrátenie JSON odpovede z API
  return response.json();
}
