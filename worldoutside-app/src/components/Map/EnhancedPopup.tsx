import {
  Card,
  Paper,
  Group,
  Stack,
  Title,
  Text,
  Badge,
  Button,
  Avatar,
  Divider,
  Space,
  Anchor,
  Grid,
  Tooltip,
  ActionIcon
} from '@mantine/core';
import {
  IconMountain,
  IconMapPin,
  IconRuler,
  IconCloudRain,
  IconRoute,
  IconPhoto,
  IconCopy,
  IconCheck,
  IconClock,
  IconPlus,
  IconMinus
} from '@tabler/icons-react';

/**
 * Props interface for the EnhancedPopup component
 */
interface EnhancedPopupProps {
  place: Place;
  visitType?: string | null;
  isPlanned: boolean;
  verifying: number | null;
  onVisitedClick: (id: number) => void;
  onVerifyClick: (id: number) => void;
  onPhotoVerifyClick: (id: number) => void;
  onAddPlanned: (id: number) => void;
  onRemovePlanned: (id: number) => void;
  addMutationPending: boolean;
  removeMutationPending: boolean;
}

/**
 * EnhancedPopup Component
 * 
 * Displays detailed information about a mountain peak in an enhanced popup.
 * Features:
 * - Peak information: name, elevation, coordinates, difficulty
 * - Visit status badges and actions
 * - External links for weather, hiking routes, and photos
 * - Responsive design optimized for mobile devices
 * - Copy coordinates functionality
 * 
 * @param place The place/peak data to display
 * @param visitType Current visit status
 * @param isPlanned Whether the peak is in planned list
 * @param verifying ID of peak being verified (for loading state)
 * @param onVisitedClick Handler for soft visit action
 * @param onVerifyClick Handler for GPS verification
 * @param onPhotoVerifyClick Handler for photo verification
 * @param onAddPlanned Handler for adding to planned list
 * @param onRemovePlanned Handler for removing from planned list
 * @param addMutationPending Loading state for add planned mutation
 * @param removeMutationPending Loading state for remove planned mutation
 */
export default function EnhancedPopup({
  place,
  visitType,
  isPlanned,
  verifying,
  onVisitedClick,
  onVerifyClick,
  onPhotoVerifyClick,
  onAddPlanned,
  onRemovePlanned,
  addMutationPending,
  removeMutationPending
}: EnhancedPopupProps) {

  /**
   * Copies coordinates to clipboard
   */
  const copyCoordinates = async () => {
    const coords = `${place.latitude}, ${place.longitude}`;
    try {
      await navigator.clipboard.writeText(coords);
      // Could add a notification here
    } catch (err) {
      console.error('Failed to copy coordinates:', err);
    }
  };

  /**
   * Gets the appropriate badge for visit status
   */
  const getVisitBadge = () => {
    if (visitType === 'verified') {
      return <Badge color="lime" variant="filled">Verified Visit</Badge>;
    } else if (visitType === 'soft') {
      return <Badge color="yellow" style={{ backgroundColor: '#facc15', color: '#000', border: '1px solid #facc15' }}>Soft Visit</Badge>;
    } else if (visitType === 'pending') {
      return <Badge color="gray">Pending Verification</Badge>;
    } else if (isPlanned) {
      return <Badge color="blue" variant="light">Planned</Badge>;
    }
    return null;
  };

  /**
   * Gets the difficulty badge with appropriate color
   */
  const getDifficultyBadge = () => {
    if (!place.difficulty) return null;
    
    const colors = {
      easy: 'green',
      moderate: 'yellow',
      hard: 'orange',
      expert: 'red'
    };

    return (
      <Badge color={colors[place.difficulty]} variant="light" size="sm">
        {place.difficulty.charAt(0).toUpperCase() + place.difficulty.slice(1)}
      </Badge>
    );
  };

  /**
   * Generates external links for the peak
   */
  const getExternalLinks = () => {
    const lat = parseFloat(place.latitude);
    const lng = parseFloat(place.longitude);
    
    return {
      weather: `https://weather.com/weather/today/l/${lat},${lng}`,
      hiking: `https://www.alltrails.com/explore?b_tl_lat=${lat + 0.01}&b_tl_lng=${lng - 0.01}&b_br_lat=${lat - 0.01}&b_br_lng=${lng + 0.01}`,
      photos: `https://www.google.com/search?q="${place.name}"+mountain+photos&tbm=isch`
    };
  };

  const links = getExternalLinks();

  return (
    <Paper
      className="modern-popup"
      shadow="lg"
      p="lg"
      radius="xl"
      style={{
        position: 'relative',
        zIndex: 2000,
        pointerEvents: 'auto',
        background: 'linear-gradient(0deg,rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.5) 50%, rgba(0, 0, 0, 0.5) 100%)',
        transition: 'all var(--transition-normal)',
        boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
        borderRadius: 'var(--radius-xl)',
        overflow: 'hidden',
        maxWidth: 340,
        minWidth: 0,
      }}
    >
      {/* Modern header with peak image and basic info */}
      <Group align="flex-start" gap={8} wrap="nowrap" mb={8} style={{ flexDirection: 'row' }}>
        <div
          style={{
            position: 'relative',
            borderRadius: 'var(--radius-xl)',
            overflow: 'hidden',
            background: 'var(--color-primary-100)',
            border: '2px solid var(--color-primary-200)',
            minWidth: 40,
            minHeight: 40,
          }}
        >
          <Avatar
            color="blue"
            radius="xl"
            size={48}
            src={place.image || undefined}
            style={{
              background: place.image ? 'transparent' : 'var(--color-primary-500)',
            }}
          >
            <IconMountain size={22} color="white" />
          </Avatar>
          {/* Status indicator */}
          {visitType && (
            <div
              style={{
                position: 'absolute',
                bottom: -2,
                right: -2,
                width: 18,
                height: 18,
                borderRadius: '50%',
                background: visitType === 'verified' ? 'var(--color-success-500)' :
                           visitType === 'soft' ? 'var(--color-warning-500)' :
                           'var(--color-primary-500)',
                border: '2px solid white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {visitType === 'verified' && <IconCheck size={10} color="white" />}
              {visitType === 'soft' && <IconClock size={10} color="white" />}
            </div>
          )}
        </div>

        <div style={{ flex: 1, minWidth: 0 }}>
          <Title
            order={4}
            style={{
              marginBottom: 'var(--spacing-xs)',
              color: 'var(--color-neutral-50)',
              fontSize: '15px',
              fontWeight: 600,
              lineHeight: 1.2,
              wordBreak: 'break-word',
            }}
          >
            {place.name}
          </Title>
          <Text
            size="xs"
            lineClamp={2}
            style={{
              color: 'var(--color-neutral-600)',
              lineHeight: 1.3,
              fontSize: '12px',
              wordBreak: 'break-word',
            }}
          >
            {place.description}
          </Text>
        </div>
      </Group>

      {/* Modern details section */}
      <div
        style={{
          background: 'var(--color-neutral-50)',
          borderRadius: 'var(--radius-lg)',
          padding: 'var(--spacing-sm)',
          marginBottom: 'var(--spacing-sm)',
          border: '1px solid var(--color-neutral-200)',
        }}
      >
        <Grid gutter={6}>
          {/* Elevation */}
          {place.elevation && (
            <Grid.Col span={6}>
              <Group gap={4} align="center">
                <div
                  style={{
                    padding: '2px 6px',
                    borderRadius: 'var(--radius-md)',
                    background: 'var(--color-primary-100)',
                    color: 'var(--color-primary-600)',
                  }}
                >
                  <IconRuler size={13} />
                </div>
                <div>
                  <Text
                    size="xs"
                    style={{
                      color: 'var(--color-neutral-500)',
                      fontWeight: 500,
                      marginBottom: '1px',
                      fontSize: '11px',
                    }}
                  >
                    Nadmorská výška
                  </Text>
                  <Text
                    size="xs"
                    fw={600}
                    style={{ color: 'var(--color-neutral-800)', fontSize: '12px' }}
                  >
                    {place.elevation}m
                  </Text>
                </div>
              </Group>
            </Grid.Col>
          )}

          {/* Coordinates */}
          <Grid.Col span={6}>
              <Group gap={4} align="center">
              <div
                style={{
                  padding: '2px 6px',
                  borderRadius: 'var(--radius-md)',
                  background: 'var(--color-success-100)',
                  color: 'var(--color-success-600)',
                }}
              >
                <IconMapPin size={13} />
              </div>
              <div style={{ flex: 1 }}>
                  <Text
                    size="xs"
                    style={{
                      color: 'var(--color-neutral-500)',
                      fontWeight: 500,
                      marginBottom: '1px',
                      fontSize: '11px',
                    }}
                  >
                    Súradnice
                  </Text>
                  <Group gap={2} align="center">
                    <Text
                      size="xs"
                      fw={600}
                      style={{
                        fontSize: '10px',
                        color: 'var(--color-neutral-800)',
                        fontFamily: 'var(--font-family-mono)',
                      }}
                    >
                      {parseFloat(place.latitude).toFixed(4)}, {parseFloat(place.longitude).toFixed(4)}
                    </Text>
                    <Tooltip label="Kopírovať súradnice" radius="md">
                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="xs"
                        radius="md"
                        onClick={copyCoordinates}
                        style={{
                          transition: 'all var(--transition-fast)',
                          color: 'var(--color-neutral-400)',
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: 'var(--color-primary-100)',
                              color: 'var(--color-primary-600)',
                              transform: 'scale(1.1)',
                            },
                          },
                        }}
                      >
                        <IconCopy size={10} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
              </div>
            </Group>
          </Grid.Col>
        </Grid>
      </div>

      {/* Badges section */}
      <Group gap={4} style={{ marginBottom: 8, flexWrap: 'wrap' }}>
        {getVisitBadge()}
        {getDifficultyBadge()}
      </Group>

      {/* External links */}
      <Group gap={2} style={{ marginBottom: 8 }}>
        <Text size="xs" color="dimmed" style={{ fontSize: '11px', color: 'var(--color-neutral-100)' }}>Rýchle odkazy:</Text>
        <Tooltip label="Predpoveď počasia">
          <Anchor href={links.weather} target="_blank" size="xs">
            <Group gap={2} align="center">
              <IconCloudRain size={11} color="var(--color-neutral-100)" />
              <Text size="xs" style={{ fontSize: '10px', color: 'var(--color-neutral-100)' }}>Počasie</Text>
            </Group>
          </Anchor>
        </Tooltip>
        <Tooltip label="Turistické trasy">
          <Anchor href={links.hiking} target="_blank" size="xs">
            <Group gap={2} align="center">
              <IconRoute size={11} color="var(--color-neutral-100)" />
              <Text size="xs" style={{ fontSize: '10px', color: 'var(--color-neutral-100)' }}>Trasy</Text>
            </Group>
          </Anchor>
        </Tooltip>
        <Tooltip label="Zobraziť fotky">
          <Anchor href={links.photos} target="_blank" size="xs">
            <Group gap={2} align="center">
              <IconPhoto size={11} color="var(--color-neutral-100)" />
              <Text size="xs" style={{ fontSize: '10px', color: 'var(--color-neutral-100)' }}>Fotky</Text>
            </Group>
          </Anchor>
        </Tooltip>
      </Group>

      <Space h={4} />

      {/* Action icons row */}
      <Group style={{ justifyContent: 'space-between', marginTop: 12, gap: 12, display: 'flex' }}>
        <Tooltip label="Som tu (neoverená návšteva)">
          <ActionIcon
            color="yellow"
            variant="filled"
            onClick={() => onVisitedClick(place.id)}
            size="lg"
            style={{ color: '#000', border: '1px solid #facc15' }}
          >
            <IconMapPin size={22} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label={isPlanned ? 'Odstrániť z plánovaných' : 'Pridať do plánovaných'}>
          <ActionIcon
            color={isPlanned ? 'red' : 'blue'}
            variant="filled"
            onClick={() => isPlanned ? onRemovePlanned(place.id) : onAddPlanned(place.id)}
            size="lg"
            loading={isPlanned ? removeMutationPending : addMutationPending}
          >
            {isPlanned ? <IconMinus size={22} /> : <IconPlus size={22} />}
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Overiť návštevu (GPS)">
          <ActionIcon
            color="teal"
            variant="filled"
            onClick={() => onVerifyClick(place.id)}
            size="lg"
            loading={verifying === place.id}
          >
            <IconCheck size={22} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Overiť fotkou">
          <ActionIcon
            color="blue"
            variant="outline"
            onClick={() => onPhotoVerifyClick(place.id)}
            size="lg"
          >
            <IconPhoto size={22} />
          </ActionIcon>
        </Tooltip>
      </Group>
    </Paper>
  );
}
