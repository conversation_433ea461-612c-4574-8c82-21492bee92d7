import L from "leaflet";

export function createMarkerWithBadgeIcon({
  iconUrl,
  visitorCount,
  iconSize = [25, 41],
  badgeColor = 'var(--color-primary-600)',
  badgeTextColor = 'var(--color-neutral-50)',
}: {
  iconUrl: string;
  visitorCount: number;
  iconSize?: [number, number];
  badgeColor?: string;
  badgeTextColor?: string;
}) {
  // HTML for marker with badge UNDER the marker icon
  // Increase container height and add border for debugging
  const containerHeight = iconSize[1] + 40;
  const badgeHtml = `<div style="position: relative; display: inline-block; width: ${iconSize[0]}px; height: ${containerHeight}px; overflow: visible;">
    <img src='${iconUrl}' width='${iconSize[0]}' height='${iconSize[1]}' style='display: block; position: relative; z-index: 2;' alt='' />
    <div style="
      position: absolute;
      left: 50%;
      top: ${iconSize[1]}px;
      transform: translate(-50%, 12px);
      background: ${badgeColor};
      color: ${badgeTextColor};
      border-radius: 12px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
      padding: 2px 7px;
      font-size: 13px;
      font-weight: 600;
      min-width: 22px;
      min-height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #fff;
      pointer-events: none;
      user-select: none;
      z-index: 1;
      background-clip: padding-box;
    " aria-label='${visitorCount} people have visited this location'>${visitorCount}</div>
  </div>`;

  // Adjust iconAnchor so the marker tip stays at the same place
  const iconAnchor: [number, number] = [iconSize[0] / 2, iconSize[1]];

  return L.divIcon({
    html: badgeHtml,
    className: '',
    iconSize: [iconSize[0], containerHeight],
    iconAnchor,
    popupAnchor: [0, -iconSize[1] / 2],
  });

  return L.divIcon({
    html: badgeHtml,
    className: '',
    iconSize,
    iconAnchor: [iconSize[0] / 2, iconSize[1]],
    popupAnchor: [0, -iconSize[1] / 2],
  });
}

export {};
