<?php

namespace App\Services;

use App\Models\User;
use App\Models\Badge;
use App\Models\Place;
use Illuminate\Support\Carbon;

/**
 * Služba pre správu systému odznakov v aplikácii WorldOutside
 *
 * Táto služba je zodpovedná za kontrolu a udeľovanie odznakov používateľom
 * na základe ich aktivít (návštev vrcholov). Implementuje rôzne kritériá
 * pre získanie odznakov ako počet navštívených vrcholov alebo dosiahnutie
 * určitej nadmorskej výšky.
 */
class BadgeService
{
    /**
     * Kontroluje a udeľuje odznaky používateľovi po novej návšteve vrcholu
     *
     * Táto metóda sa volá po každej novej overenej návšteve vrcholu.
     * Kontroluje všetky dostupné odznaky a udeľuje tie, na ktoré má
     * používateľ nárok na základe svojich dosiahnutých výsledkov.
     *
     * Aktuálne podporované odznaky:
     * - "first-5-peaks": Za prvých 5 overených vrcholov
     * - "first-10-peaks": Za prvých 10 overených vrcholov
     * - "first-2000m-peak": Za prvý vrchol nad 2000m nadmorskej výšky
     *
     * @param User $user Používateľ, pre ktorého kontrolujeme odznaky
     * @return array<Badge> Pole novo udelených odznakov
     */
    public function checkAndAwardBadges(User $user): array
    {
        $awarded = [];

        // Získanie všetkých overených návštev používateľa
        // Berieme do úvahy len návštevy s typom 'verified' (nie 'planned' alebo 'visited')
        $visitedPlaces = $user->places()->wherePivot('visit_type', 'verified')->get();
        $visitedCount = $visitedPlaces->count();

        // ODZNAK: Prvých 5 vrcholov
        // Kontrola a udelenie odznaku za dosiahnutie 5 overených vrcholov
        $badge5 = Badge::where('slug', 'first-5-peaks')->first();
        if ($badge5 && $visitedCount >= 5 && !$user->badges->contains($badge5->id)) {
            // Pripojenie odznaku k používateľovi s časom udelenia
            $user->badges()->attach($badge5->id, ['awarded_at' => Carbon::now()]);
            $awarded[] = $badge5;
        }

        // ODZNAK: Prvých 10 vrcholov
        // Kontrola a udelenie odznaku za dosiahnutie 10 overených vrcholov
        $badge10 = Badge::where('slug', 'first-10-peaks')->first();
        if ($badge10 && $visitedCount >= 10 && !$user->badges->contains($badge10->id)) {
            // Pripojenie odznaku k používateľovi s časom udelenia
            $user->badges()->attach($badge10->id, ['awarded_at' => Carbon::now()]);
            $awarded[] = $badge10;
        }

        // ODZNAK: Prvý vrchol nad 2000m
        // Kontrola a udelenie odznaku za prvý vrchol s nadmorskou výškou 2000m a viac
        $badge2000 = Badge::where('slug', 'first-2000m-peak')->first();
        if ($badge2000 && !$user->badges->contains($badge2000->id)) {
            // Kontrola, či používateľ navštívil aspoň jeden vrchol nad 2000m
            $has2000 = $visitedPlaces->contains(function($place) {
                return $place->elevation >= 2000;
            });

            // Ak má vrchol nad 2000m, udelíme odznak
            if ($has2000) {
                $user->badges()->attach($badge2000->id, ['awarded_at' => Carbon::now()]);
                $awarded[] = $badge2000;
            }
        }

        // Vrátenie zoznamu novo udelených odznakov
        return $awarded;
    }
}
