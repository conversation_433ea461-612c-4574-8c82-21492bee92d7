<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * Model používateľa aplikácie WorldOutside
 *
 * Reprezentuje používateľa, ktorý môže navštevovať miesta (vrcholy) a získavať odznaky.
 * Rozširuje základný Laravel Authenticatable model o funkcionalitu pre správu
 * návštev miest a systém odznakov.
 *
 * @property int $id Jedinečný identifikátor používateľa
 * @property string $name Meno používateľa
 * @property string $email Email adresa používateľa
 * @property string $password Hashované heslo
 * @property string|null $profile_image URL profilového obrázka
 * @property string|null $bio Biografický popis používateľa
 * @property \Illuminate\Support\Carbon|null $email_verified_at Čas overenia emailu
 * @property string|null $remember_token Token pre "zapamätaj si ma"
 * @property \Illuminate\Support\Carbon|null $created_at Čas vytvorenia záznamu
 * @property \Illuminate\Support\Carbon|null $updated_at Čas poslednej aktualizácie
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * Atribúty, ktoré môžu byť hromadne priradené (mass assignment)
     *
     * Definuje, ktoré atribúty môžu byť bezpečne nastavené pomocou
     * metód ako create() alebo fill().
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',           // Meno používateľa
        'email',          // Email adresa
        'password',       // Heslo (bude automaticky hashované)
        'profile_image',  // URL profilového obrázka
        'bio',           // Biografický popis
    ];

    /**
     * Atribúty, ktoré majú byť skryté pri serializácii
     *
     * Tieto atribúty nebudú zahrnuté do JSON výstupu pri konverzii
     * modelu na pole alebo JSON (napr. pri API odpovediach).
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',       // Heslo - nikdy sa nesmie vrátiť v API
        'remember_token', // Token pre "zapamätaj si ma" - bezpečnostný údaj
    ];

    /**
     * Vzťah many-to-many s miestami (vrcholmi)
     *
     * Definuje vzťah medzi používateľom a miestami, ktoré navštívil.
     * Pivot tabuľka obsahuje dodatočné informácie ako typ návštevy
     * (planned/visited/verified) a časové značky.
     *
     * @return BelongsToMany Kolekcia miest spojených s používateľom
     */
    public function places(): BelongsToMany {
        return $this->belongsToMany(Place::class)
            ->withPivot('visit_type')  // Typ návštevy (planned/visited/verified)
            ->withTimestamps();        // Automatické časové značky
    }

    /**
     * Vzťah many-to-many s odznakami
     *
     * Definuje vzťah medzi používateľom a odznakami, ktoré získal.
     * Pivot tabuľka obsahuje informáciu o čase udelenia odznaku.
     *
     * @return BelongsToMany Kolekcia odznakov spojených s používateľom
     */
    public function badges(): BelongsToMany
    {
        return $this->belongsToMany(Badge::class, 'user_badge')
            ->withTimestamps()           // Automatické created_at a updated_at
            ->withPivot('awarded_at');   // Čas udelenia odznaku
    }

    /**
     * Definuje, ako majú byť atribúty automaticky konvertované
     *
     * Špecifikuje typy dát, na ktoré majú byť atribúty automaticky
     * konvertované pri načítaní z databázy alebo nastavení.
     *
     * @return array<string, string> Mapa atribútov a ich typov
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime', // Konverzia na Carbon datetime objekt
            'password' => 'hashed',            // Automatické hashovanie hesla pri nastavení
        ];
    }
}
