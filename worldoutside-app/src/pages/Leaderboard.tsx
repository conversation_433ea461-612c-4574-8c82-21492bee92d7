
import React, { useState } from "react";
import {
  Card,
  Stack,
  Title,
  Text,
  Group,
  Loader,
  Center,
  Alert,
  Avatar,
  Badge,
  Select,
  SegmentedControl,
  Pagination,
  Tooltip,
  ScrollArea,
} from "@mantine/core";
import { IconTrophy, IconUser } from "@tabler/icons-react";

import { useLeaderboardQuery } from "../actions/getLeaderboard";

const badgeColors: Record<string, string> = {
  summit: "blue",
  marathon: "orange",
  winter: "cyan",
  speedster: "green",
};



export default function Leaderboard() {
  // Filters and state
  const [metric, setMetric] = useState("peaks");
  const [region, setRegion] = useState("all");
  const [period, setPeriod] = useState("all");
  const [page, setPage] = useState(1);
  const perPage = 10;

  const {
    data,
    isLoading,
    error,
  } = useLeaderboardQuery({ metric, region, period, page, per_page: perPage });

  const leaderboard = data?.data ?? [];
  const totalPages = data?.meta?.last_page ?? 1;
  const regions = data?.regions ?? [];

  // Responsive container style similar to Profile
  const containerStyle: React.CSSProperties = {
    maxWidth: 480,
    margin: "0 auto",
    width: "100%",
    padding: "var(--spacing-lg)",
    minHeight: "100vh",
    background: "var(--color-neutral-50)",
    boxSizing: "border-box",
  };

  return (
    <div style={containerStyle}>
      <Stack gap="md">
      <Title order={2} mt="md" mb="md" style={{ textAlign: "center" }}>Leaderboard</Title>
      <Group gap="md" wrap="wrap" style={{ justifyContent: "center" }}>
        <SegmentedControl
          value={metric}
          onChange={setMetric}
          data={[
            { label: "Peaks Climbed", value: "peaks" },
            { label: "Elevation Gained", value: "elevation" },
          ]}
          aria-label="Leaderboard metric"
        />
        <Select
          value={region}
          onChange={(v) => setRegion(v || "all")}
          data={[
            { value: "all", label: "All Regions" },
            ...regions.map((r: string) => ({ value: r, label: r })),
          ]}
          aria-label="Region filter"
        />
        <Select
          value={period}
          onChange={(v) => setPeriod(v || "all")}
          data={[
            { value: "all", label: "All Time" },
            { value: "year", label: "This Year" },
            { value: "month", label: "This Month" },
          ]}
          aria-label="Time period filter"
        />
      </Group>
      {isLoading ? (
        <Center style={{ minHeight: 200 }}><Loader /></Center>
      ) : error ? (
        <Alert color="red" title="Error" w="100%">{typeof error === 'string' ? error : 'An error occurred.'}</Alert>
      ) : leaderboard.length === 0 ? (
        <Center style={{ minHeight: 200 }}><Text c="dimmed">No users found for selected filters.</Text></Center>
      ) : (
        <Stack gap="sm">
          {leaderboard.map((user, idx) => (
            <Card
              key={user.id}
              shadow={user.isCurrentUser ? "lg" : "md"}
              padding="md"
              radius="lg"
              withBorder
              style={{
                background:
                  idx === 0
                    ? "var(--color-primary-100)"
                    : user.isCurrentUser
                    ? "var(--color-primary-50)"
                    : "var(--color-neutral-100)",
                borderColor: user.isCurrentUser ? "var(--color-primary-400)" : undefined,
                boxShadow: user.isCurrentUser ? "var(--shadow-lg)" : undefined,
                transition: "box-shadow var(--transition-fast), transform var(--transition-fast)",
                transform: user.isCurrentUser ? "scale(1.02)" : undefined,
              }}
              tabIndex={0}
              aria-label={`User ${user.name}, rank ${idx + 1 + (page - 1) * perPage}`}
            >
              <Group gap="md" align="center" wrap="nowrap">
                <Text fw={700} size="xl" style={{ width: 32, textAlign: "center" }}>
                  {idx + 1 + (page - 1) * perPage}
                </Text>
                <Avatar src={user.avatar} radius="xl" size={56} color="blue">
                  <IconUser size={32} />
                </Avatar>
                <Stack gap={2} style={{ flex: 1 }}>
                  <Group gap={4} align="center">
                    <Text fw={600} size="lg">{user.name}</Text>
                    {idx === 0 && (
                      <Tooltip label="Top Performer">
                        <IconTrophy size={20} color="gold" style={{ marginLeft: 4 }} />
                      </Tooltip>
                    )}
                    {user.isCurrentUser && (
                      <Badge color="blue" size="sm" ml={4} radius="sm">You</Badge>
                    )}
                  </Group>
                  <Group gap={8} align="center">
                    <Text size="sm" c="dimmed">Peaks: <b>{user.peaks}</b></Text>
                    <Text size="sm" c="dimmed">Elevation: <b>{user.elevation} m</b></Text>
                  </Group>
                  <Group gap={4}>
                    {user.badges.map((badge) => (
                      <Badge key={badge.slug} color={badgeColors[badge.slug] || "gray"} size="xs" radius="sm">
                        {badge.name}
                      </Badge>
                    ))}
                  </Group>
                </Stack>
              </Group>
            </Card>
          ))}
        </Stack>
      )}
      <Group justify="center" mt="md">
        <Pagination
          value={page}
          onChange={setPage}
          total={totalPages}
          size="md"
          radius="md"
          aria-label="Leaderboard pagination"
        />
      </Group>
      </Stack>
    </div>
  );
}
