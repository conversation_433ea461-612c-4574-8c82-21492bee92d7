<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Model miesta (vrcholu) v aplikácii WorldOutside
 *
 * Reprezentuje geografické miesto (typicky vrchol), ktoré môžu používatelia navštevovať.
 * Obsahuje geografické súradnice, výšku a ďalšie informácie o mieste.
 * Podporuje systém návštev s rôznymi typmi (plánované/navštívené/overené).
 *
 * @property int $id Jedinečný identifikátor miesta
 * @property string $name Názov miesta/vrcholu
 * @property string|null $description Popis miesta
 * @property float $latitude Zemepisná šírka (GPS súradnica)
 * @property float $longitude Zemepisná dĺžka (GPS súradnica)
 * @property int|null $elevation Nadmorská výška v metroch
 * @property string|null $image URL obrázka miesta
 * @property \Illuminate\Support\Carbon|null $created_at Čas vytvorenia záznamu
 * @property \Illuminate\Support\Carbon|null $updated_at Čas poslednej aktualizácie
 * @property bool $visited Dočasný atribút označujący, či miesto navštívil aktuálny používateľ
 */
class Place extends Model {
    use HasFactory;

    /**
     * Dočasný atribút označujúci, či bolo miesto navštívené aktuálnym používateľom
     *
     * Tento atribút nie je uložený v databáze, ale je nastavovaný dynamicky
     * na základe vzťahu s používateľom. Používa sa pre rýchle určenie stavu návštevy.
     *
     * @var bool
     */
    public bool $visited = false;

    /**
     * Atribúty, ktoré môžu byť hromadne priradené (mass assignment)
     *
     * Definuje, ktoré atribúty môžu byť bezpečne nastavené pomocou
     * metód ako create() alebo fill().
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',        // Názov miesta/vrcholu
        'description', // Popis miesta
        'latitude',    // Zemepisná šírka (GPS súradnica)
        'longitude',   // Zemepisná dĺžka (GPS súradnica)
        'elevation',   // Nadmorská výška v metroch
        'image',       // URL obrázka miesta
    ];

    /**
     * Vzťah many-to-many s používateľmi
     *
     * Definuje vzťah medzi miestom a používateľmi, ktorí ho navštívili.
     * Pivot tabuľka obsahuje typ návštevy a časové značky.
     *
     * @return BelongsToMany Kolekcia používateľov spojených s miestom
     */
    public function users(): BelongsToMany {
        return $this->belongsToMany(User::class)
            ->withPivot('visit_type')  // Typ návštevy (planned/visited/verified)
            ->withTimestamps();        // Automatické časové značky
    }

    /**
     * Zistí, či konkrétny používateľ navštívil toto miesto
     *
     * Kontroluje, či sa ID tohto miesta nachádza v kolekcii miest
     * navštívených daným používateľom. Nastavuje aj dočasný atribút $visited.
     *
     * @param User $user Používateľ, pre ktorého kontrolujeme návštevu
     * @return bool True, ak používateľ navštívil miesto, inak false
     */
    public function visitedByUser(User $user): bool {
        // Kontrola, či sa ID miesta nachádza v kolekcii používateľových miest
        $this->visited = $user->places->contains($this->id);

        return $this->visited;
    }

    /**
     * Vypočíta vzdialenosť medzi dvoma GPS súradnicami pomocou Haversine vzorca
     *
     * Haversine vzorec je matematická formula na výpočet najkratšej vzdialenosti
     * medzi dvoma bodmi na povrchu gule (Zeme) na základe ich zemepisných súradníc.
     * Vzorec bere do úvahy zakrivenie Zeme.
     *
     * @param float $lat1 Zemepisná šírka prvého bodu v stupňoch
     * @param float $lon1 Zemepisná dĺžka prvého bodu v stupňoch
     * @param float $lat2 Zemepisná šírka druhého bodu v stupňoch
     * @param float $lon2 Zemepisná dĺžka druhého bodu v stupňoch
     * @return float Vzdialenosť v metroch
     */
    public static function haversineDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // Polomer Zeme v metroch (stredná hodnota)
        $earthRadius = 6371000;

        // Konverzia stupňov na radiány pre všetky súradnice
        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLat = deg2rad($lat2 - $lat1);  // Rozdiel zemepisných šírok
        $deltaLon = deg2rad($lon2 - $lon1);  // Rozdiel zemepisných dĺžok

        // Haversine vzorec - výpočet pomocnej premennej 'a'
        // Táto časť vzorca počíta štvorec polovice tetivovej vzdialenosti
        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
            cos($lat1Rad) * cos($lat2Rad) *
            sin($deltaLon / 2) * sin($deltaLon / 2);

        // Výpočet uhlového odstupu v radiánoch
        // atan2 je robustnejší ako acos pre malé vzdialenosti
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        // Konečná vzdialenosť = polomer * uhlový odstup
        return $earthRadius * $c;
    }
}
