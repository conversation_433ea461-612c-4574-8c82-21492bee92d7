type Place = {
  created_at: string;
  id: number;
  description: string;
  image: string;
  latitude: string;
  longitude: string;
  name: string;
  updated_at: string;
  visited: boolean;
  elevation?: number; // Elevation in meters
  difficulty?: 'easy' | 'moderate' | 'hard' | 'expert'; // Difficulty rating
  visit_type?: 'planned' | 'soft' | 'verified' | 'pending'; // Visit status
  visitor_count?: number; // Number of people who have visited this location
};
